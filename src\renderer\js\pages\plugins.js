// Plugins Management Page
class PluginsPage {
    static async render(container) {
        const html = `
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Total Plugins</div>
                        <div class="stat-card-icon primary">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="plugins-total-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-list"></i>
                        <span>Installed</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Enabled Plugins</div>
                        <div class="stat-card-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="plugins-enabled-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-play"></i>
                        <span>Active</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Disabled Plugins</div>
                        <div class="stat-card-icon warning">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="plugins-disabled-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-pause"></i>
                        <span>Inactive</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Plugin Storage</div>
                        <div class="stat-card-icon error">
                            <i class="fas fa-hdd"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="plugins-storage-size">0 MB</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-weight"></i>
                        <span>Used</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Plugin Management</h3>
                    <p class="card-subtitle">Install, configure, and manage server plugins</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-secondary" onclick="PluginsPage.uploadPlugin()">
                            <i class="fas fa-upload"></i>
                            Upload Plugin
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="PluginsPage.refreshPlugins()">
                            <i class="fas fa-sync"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="plugin-controls">
                        <div class="plugin-filters">
                            <div class="filter-group">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" id="plugin-search"
                                       placeholder="Search plugins..."
                                       onkeyup="PluginsPage.filterPlugins()">
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Status</label>
                                <select class="form-control" id="plugin-status-filter" onchange="PluginsPage.filterPlugins()">
                                    <option value="">All Plugins</option>
                                    <option value="enabled">Enabled</option>
                                    <option value="disabled">Disabled</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Category</label>
                                <select class="form-control" id="plugin-category-filter" onchange="PluginsPage.filterPlugins()">
                                    <option value="">All Categories</option>
                                    <option value="admin">Administration</option>
                                    <option value="gameplay">Gameplay</option>
                                    <option value="economy">Economy</option>
                                    <option value="protection">Protection</option>
                                    <option value="utility">Utility</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id="plugins-table"></div>
                </div>
            </div>

            <div class="dashboard-row">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Plugin Categories</h3>
                        <p class="card-subtitle">Plugins organized by category</p>
                    </div>
                    <div class="card-body">
                        <div class="plugin-categories" id="plugin-categories">
                            <!-- Categories will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                        <p class="card-subtitle">Common plugin operations</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions-grid">
                            <button class="btn btn-secondary" onclick="PluginsPage.reloadAllPlugins()">
                                <i class="fas fa-sync-alt"></i>
                                Reload All
                            </button>
                            <button class="btn btn-secondary" onclick="PluginsPage.enableAllPlugins()">
                                <i class="fas fa-play-circle"></i>
                                Enable All
                            </button>
                            <button class="btn btn-secondary" onclick="PluginsPage.disableAllPlugins()">
                                <i class="fas fa-pause-circle"></i>
                                Disable All
                            </button>
                            <button class="btn btn-secondary" onclick="PluginsPage.checkUpdates()">
                                <i class="fas fa-download"></i>
                                Check Updates
                            </button>
                            <button class="btn btn-secondary" onclick="PluginsPage.exportPluginList()">
                                <i class="fas fa-file-export"></i>
                                Export List
                            </button>
                            <button class="btn btn-secondary" onclick="PluginsPage.openPluginsFolder()">
                                <i class="fas fa-folder-open"></i>
                                Open Folder
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plugin Configuration Modal -->
            <div class="modal-overlay" id="plugin-config-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title" id="config-modal-title">Plugin Configuration</h3>
                        <button class="modal-close" onclick="PluginsPage.closeConfigModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="config-editor">
                            <div class="form-group">
                                <label class="form-label">Configuration File</label>
                                <textarea class="form-control config-textarea" id="plugin-config-content"
                                          rows="20" placeholder="Plugin configuration will be loaded here..."></textarea>
                                <div class="form-text">
                                    Make sure to follow YAML syntax. Invalid configuration may prevent the plugin from loading.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="PluginsPage.closeConfigModal()">
                            Cancel
                        </button>
                        <button class="btn btn-warning" onclick="PluginsPage.resetConfig()">
                            <i class="fas fa-undo"></i>
                            Reset
                        </button>
                        <button class="btn btn-primary" onclick="PluginsPage.saveConfig()">
                            <i class="fas fa-save"></i>
                            Save Configuration
                        </button>
                    </div>
                </div>
            </div>

            <!-- Plugin Details Modal -->
            <div class="modal-overlay" id="plugin-details-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title" id="details-modal-title">Plugin Details</h3>
                        <button class="modal-close" onclick="PluginsPage.closeDetailsModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="plugin-details-content">
                            <!-- Plugin details will be loaded here -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="PluginsPage.closeDetailsModal()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;

        // Initialize the page
        await this.initializePage();
    }

    static async initializePage() {
        this.plugins = [];
        this.filteredPlugins = [];
        this.selectedPlugin = null;

        // Load initial data
        await this.loadPluginStats();
        await this.loadPlugins();
        await this.loadPluginCategories();
    }

    static async loadPluginStats() {
        try {
            const total = this.plugins.length;
            const enabled = this.plugins.filter(p => p.enabled).length;
            const disabled = total - enabled;
            const totalSize = this.plugins.reduce((sum, p) => sum + (p.size || 0), 0);
            const storageSize = formatBytes(totalSize);

            document.getElementById('plugins-total-count').textContent = total;
            document.getElementById('plugins-enabled-count').textContent = enabled;
            document.getElementById('plugins-disabled-count').textContent = disabled;
            document.getElementById('plugins-storage-size').textContent = storageSize;

        } catch (error) {
            console.error('Error loading plugin stats:', error);
        }
    }

    static async loadPlugins() {
        try {
            if (window.electronAPI) {
                const plugins = await window.electronAPI.getInstalledPlugins();

                // Map the plugin data to match our UI expectations
                this.plugins = plugins.map(plugin => ({
                    name: plugin.name,
                    filename: plugin.fileName,
                    version: plugin.version,
                    author: plugin.author,
                    description: plugin.description,
                    enabled: plugin.enabled,
                    category: this.categorizePlugin(plugin.name),
                    size: plugin.size,
                    dependencies: plugin.dependencies || [],
                    softDependencies: plugin.softDependencies || [],
                    hasConfig: plugin.hasConfig,
                    lastModified: plugin.lastModified
                }));
            } else {
                // Fallback to mock data for development
                this.plugins = [];
            }

            this.filterPlugins();

        } catch (error) {
            console.error('Error loading plugins:', error);
            toastNotifications.show('Error', 'Failed to load plugins: ' + error.message, 'error');
        }
    }

    static categorizePlugin(pluginName) {
        const categories = {
            'EssentialsX': 'admin',
            'Essentials': 'admin',
            'WorldEdit': 'admin',
            'WorldGuard': 'protection',
            'LuckPerms': 'admin',
            'PermissionsEx': 'admin',
            'Vault': 'economy',
            'IridiumSkyblock': 'gameplay',
            'PlaceholderAPI': 'utility',
            'ProtocolLib': 'utility',
            'Citizens': 'gameplay',
            'Multiverse': 'admin',
            'CoreProtect': 'protection',
            'GriefPrevention': 'protection',
            'ChestShop': 'economy',
            'Jobs': 'economy'
        };

        return categories[pluginName] || 'utility';
    }

    static filterPlugins() {
        const searchTerm = document.getElementById('plugin-search')?.value.toLowerCase() || '';
        const statusFilter = document.getElementById('plugin-status-filter')?.value || '';
        const categoryFilter = document.getElementById('plugin-category-filter')?.value || '';

        this.filteredPlugins = this.plugins.filter(plugin => {
            const matchesSearch = !searchTerm ||
                plugin.name.toLowerCase().includes(searchTerm) ||
                plugin.description.toLowerCase().includes(searchTerm) ||
                plugin.author.toLowerCase().includes(searchTerm);

            const matchesStatus = !statusFilter ||
                (statusFilter === 'enabled' && plugin.enabled) ||
                (statusFilter === 'disabled' && !plugin.enabled);

            const matchesCategory = !categoryFilter || plugin.category === categoryFilter;

            return matchesSearch && matchesStatus && matchesCategory;
        });

        this.renderPlugins();
    }

    static renderPlugins() {
        const container = document.getElementById('plugins-table');
        if (!container) return;

        if (this.filteredPlugins.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No plugins match the current filters
                </div>
            `;
            return;
        }

        const table = new DataTable(container, {
            columns: [
                {
                    key: 'name',
                    title: 'Plugin Name',
                    sortable: true,
                    render: (value, row) => `
                        <div class="plugin-name-cell">
                            <div class="plugin-name">${value}</div>
                            <div class="plugin-version">v${row.version}</div>
                        </div>
                    `
                },
                { key: 'author', title: 'Author', sortable: true },
                {
                    key: 'category',
                    title: 'Category',
                    sortable: true,
                    render: (value) => `<span class="badge badge-secondary">${value}</span>`
                },
                {
                    key: 'size',
                    title: 'Size',
                    sortable: true,
                    render: (value) => formatBytes(value)
                },
                {
                    key: 'enabled',
                    title: 'Status',
                    sortable: true,
                    render: (value) => `
                        <span class="badge badge-${value ? 'success' : 'warning'}">
                            ${value ? 'Enabled' : 'Disabled'}
                        </span>
                    `
                },
                {
                    key: 'actions',
                    title: 'Actions',
                    render: (value, row) => `
                        <div class="btn-group">
                            <button class="btn btn-sm btn-primary" onclick="PluginsPage.showPluginDetails('${row.name}')" title="Details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="PluginsPage.configurePlugin('${row.name}')" title="Configure">
                                <i class="fas fa-cog"></i>
                            </button>
                            <button class="btn btn-sm btn-${row.enabled ? 'warning' : 'success'}"
                                    onclick="PluginsPage.togglePlugin('${row.name}')"
                                    title="${row.enabled ? 'Disable' : 'Enable'}">
                                <i class="fas fa-${row.enabled ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn btn-sm btn-error" onclick="PluginsPage.deletePlugin('${row.name}')" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `
                }
            ],
            data: this.filteredPlugins,
            searchable: false, // We handle search ourselves
            pagination: true,
            pageSize: 10
        });
    }

    static async loadPluginCategories() {
        try {
            const container = document.getElementById('plugin-categories');
            if (!container) return;

            // Count plugins by category
            const categories = {};
            this.plugins.forEach(plugin => {
                categories[plugin.category] = (categories[plugin.category] || 0) + 1;
            });

            const categoryNames = {
                admin: 'Administration',
                gameplay: 'Gameplay',
                economy: 'Economy',
                protection: 'Protection',
                utility: 'Utility'
            };

            const categoryList = Object.entries(categories).map(([category, count]) => `
                <div class="category-item" onclick="PluginsPage.filterByCategory('${category}')">
                    <div class="category-name">${categoryNames[category] || category}</div>
                    <div class="category-count">${count} plugin${count !== 1 ? 's' : ''}</div>
                </div>
            `).join('');

            container.innerHTML = categoryList;

        } catch (error) {
            console.error('Error loading plugin categories:', error);
        }
    }

    static filterByCategory(category) {
        const categoryFilter = document.getElementById('plugin-category-filter');
        if (categoryFilter) {
            categoryFilter.value = category;
            this.filterPlugins();
        }
    }

    static async togglePlugin(pluginName) {
        try {
            const plugin = this.plugins.find(p => p.name === pluginName);
            if (!plugin) return;

            const action = plugin.enabled ? 'disable' : 'enable';

            toastNotifications.show('Plugin', `${action === 'enable' ? 'Enabling' : 'Disabling'} ${pluginName}...`, 'info');

            if (window.electronAPI) {
                if (action === 'enable') {
                    await window.electronAPI.enablePlugin(plugin.id || pluginName);
                } else {
                    await window.electronAPI.disablePlugin(plugin.id || pluginName);
                }
            }

            // Refresh the plugin list to get updated status
            await this.loadPlugins();
            await this.loadPluginStats();
            await this.loadPluginCategories();

        } catch (error) {
            console.error(`Error toggling plugin ${pluginName}:`, error);
            toastNotifications.show('Error', `Failed to ${plugin.enabled ? 'disable' : 'enable'} plugin: ` + error.message, 'error');
        }
    }

    static async configurePlugin(pluginName) {
        try {
            const plugin = this.plugins.find(p => p.name === pluginName);
            if (!plugin) return;

            if (!plugin.hasConfig) {
                toastNotifications.show('Info', `${pluginName} does not have a configuration file`, 'info');
                return;
            }

            this.selectedPlugin = pluginName;

            // Show configuration modal
            const modal = document.getElementById('plugin-config-modal');
            const title = document.getElementById('config-modal-title');
            const content = document.getElementById('plugin-config-content');

            title.textContent = `${pluginName} Configuration`;
            content.value = 'Loading configuration...';

            modal.classList.add('active');

            // Load actual configuration
            if (window.electronAPI) {
                try {
                    const configResult = await window.electronAPI.getPluginConfigContent(pluginName);
                    content.value = configResult.config;
                } catch (error) {
                    content.value = `# Error loading configuration: ${error.message}
# Please check if the plugin has a config.yml file`;
                    console.error('Error loading plugin config:', error);
                }
            }

        } catch (error) {
            console.error('Error opening plugin configuration:', error);
            toastNotifications.show('Error', 'Failed to open plugin configuration: ' + error.message, 'error');
        }
    }

    static closeConfigModal() {
        const modal = document.getElementById('plugin-config-modal');
        modal.classList.remove('active');
        this.selectedPlugin = null;
    }

    static async saveConfig() {
        if (!this.selectedPlugin) return;

        const content = document.getElementById('plugin-config-content').value;

        try {
            if (window.electronAPI) {
                await window.electronAPI.updatePluginConfigContent(this.selectedPlugin, content);
            }

            this.closeConfigModal();
        } catch (error) {
            console.error('Error saving plugin config:', error);
            toastNotifications.show('Error', 'Failed to save configuration: ' + error.message, 'error');
        }
    }

    static resetConfig() {
        showConfirmDialog(
            'Reset Configuration',
            'Are you sure you want to reset the configuration to default values?',
            () => {
                toastNotifications.show('Success', 'Configuration reset to defaults', 'success');
                this.configurePlugin(this.selectedPlugin);
            }
        );
    }

    static showPluginDetails(pluginName) {
        const plugin = this.plugins.find(p => p.name === pluginName);
        if (!plugin) return;

        const modal = document.getElementById('plugin-details-modal');
        const title = document.getElementById('details-modal-title');
        const content = document.getElementById('plugin-details-content');

        title.textContent = `${plugin.name} Details`;

        content.innerHTML = `
            <div class="plugin-details">
                <div class="detail-section">
                    <h4>Basic Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <strong>Name:</strong> ${plugin.name}
                        </div>
                        <div class="detail-item">
                            <strong>Version:</strong> ${plugin.version}
                        </div>
                        <div class="detail-item">
                            <strong>Author:</strong> ${plugin.author}
                        </div>
                        <div class="detail-item">
                            <strong>Category:</strong> ${plugin.category}
                        </div>
                        <div class="detail-item">
                            <strong>File Size:</strong> ${formatBytes(plugin.size)}
                        </div>
                        <div class="detail-item">
                            <strong>Status:</strong>
                            <span class="badge badge-${plugin.enabled ? 'success' : 'warning'}">
                                ${plugin.enabled ? 'Enabled' : 'Disabled'}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Description</h4>
                    <p>${plugin.description}</p>
                </div>

                <div class="detail-section">
                    <h4>Dependencies</h4>
                    <div class="dependencies">
                        ${plugin.dependencies.length > 0 ?
                            plugin.dependencies.map(dep => `<span class="badge badge-secondary">${dep}</span>`).join(' ') :
                            '<span class="text-muted">No dependencies</span>'
                        }
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Links</h4>
                    <div class="plugin-links">
                        <a href="${plugin.website}" target="_blank" class="btn btn-sm btn-primary">
                            <i class="fas fa-external-link-alt"></i>
                            Website
                        </a>
                    </div>
                </div>
            </div>
        `;

        modal.classList.add('active');
    }

    static closeDetailsModal() {
        const modal = document.getElementById('plugin-details-modal');
        modal.classList.remove('active');
    }

    static deletePlugin(pluginName) {
        showConfirmDialog(
            'Delete Plugin',
            `Are you sure you want to delete ${pluginName}? This action cannot be undone.`,
            async () => {
                try {
                    const plugin = this.plugins.find(p => p.name === pluginName);
                    if (!plugin) return;

                    if (window.electronAPI) {
                        await window.electronAPI.deletePlugin(plugin.id || pluginName);
                    }

                    // Refresh the plugin list
                    await this.loadPlugins();
                    await this.loadPluginStats();
                    await this.loadPluginCategories();

                } catch (error) {
                    console.error('Error deleting plugin:', error);
                    toastNotifications.show('Error', 'Failed to delete plugin: ' + error.message, 'error');
                }
            }
        );
    }

    static uploadPlugin() {
        toastNotifications.show('Info', 'Plugin upload feature coming soon', 'info');
    }

    static async refreshPlugins() {
        await this.loadPlugins();
        await this.loadPluginStats();
        await this.loadPluginCategories();
        toastNotifications.show('Success', 'Plugin list refreshed', 'success');
    }

    static reloadAllPlugins() {
        showConfirmDialog(
            'Reload All Plugins',
            'This will reload all enabled plugins. Continue?',
            async () => {
                try {
                    if (window.electronAPI) {
                        await window.electronAPI.reloadPlugins();
                    }
                } catch (error) {
                    console.error('Error reloading plugins:', error);
                    toastNotifications.show('Error', 'Failed to reload plugins: ' + error.message, 'error');
                }
            }
        );
    }

    static enableAllPlugins() {
        showConfirmDialog(
            'Enable All Plugins',
            'This will enable all currently disabled plugins. Continue?',
            () => {
                this.plugins.forEach(plugin => plugin.enabled = true);
                this.filterPlugins();
                this.loadPluginStats();
                toastNotifications.show('Success', 'All plugins enabled', 'success');
            }
        );
    }

    static disableAllPlugins() {
        showConfirmDialog(
            'Disable All Plugins',
            'This will disable all currently enabled plugins. Continue?',
            () => {
                this.plugins.forEach(plugin => plugin.enabled = false);
                this.filterPlugins();
                this.loadPluginStats();
                toastNotifications.show('Success', 'All plugins disabled', 'success');
            }
        );
    }

    static checkUpdates() {
        toastNotifications.show('Info', 'Checking for plugin updates...', 'info');
        setTimeout(() => {
            toastNotifications.show('Success', 'All plugins are up to date', 'success');
        }, 2000);
    }

    static exportPluginList() {
        try {
            const pluginList = this.plugins.map(plugin => ({
                name: plugin.name,
                version: plugin.version,
                author: plugin.author,
                enabled: plugin.enabled,
                category: plugin.category
            }));

            const data = JSON.stringify(pluginList, null, 2);
            const filename = `plugin_list_${new Date().toISOString().split('T')[0]}.json`;

            downloadFile(data, filename, 'application/json');
            toastNotifications.show('Success', 'Plugin list exported successfully', 'success');
        } catch (error) {
            console.error('Error exporting plugin list:', error);
            toastNotifications.show('Error', 'Failed to export plugin list', 'error');
        }
    }

    static openPluginsFolder() {
        toastNotifications.show('Info', 'Opening plugins folder...', 'info');
    }
}

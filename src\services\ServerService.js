const Docker = require('dockerode');
const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs').promises;
const path = require('path');

const execAsync = promisify(exec);

class ServerService {
    constructor() {
        this.docker = new Docker();
        this.containerName = 'minecraft-server';
        this.composePath = path.join(process.cwd(), 'docker-compose.yml');
        this.serverPath = path.join(process.cwd(), 'simMC');
        this.logPath = path.join(this.serverPath, 'logs', 'latest.log');
    }

    async startServer() {
        try {
            console.log('Starting Minecraft server...');
            
            // Use docker-compose to start the server
            const { stdout, stderr } = await execAsync('docker-compose up -d minecraft', {
                cwd: process.cwd()
            });
            
            if (stderr && !stderr.includes('Creating') && !stderr.includes('Starting')) {
                throw new Error(stderr);
            }
            
            console.log('Server start command executed:', stdout);
            
            // Wait a moment for the container to start
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            return {
                success: true,
                message: 'Server started successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error starting server:', error);
            throw new Error(`Failed to start server: ${error.message}`);
        }
    }

    async stopServer() {
        try {
            console.log('Stopping Minecraft server...');
            
            // First try to stop gracefully by sending stop command
            try {
                await this.executeServerCommand('stop');
                // Wait for graceful shutdown
                await new Promise(resolve => setTimeout(resolve, 10000));
            } catch (error) {
                console.log('Graceful stop failed, forcing stop...');
            }
            
            // Use docker-compose to stop the server
            const { stdout, stderr } = await execAsync('docker-compose stop minecraft', {
                cwd: process.cwd()
            });
            
            console.log('Server stop command executed:', stdout);
            
            return {
                success: true,
                message: 'Server stopped successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error stopping server:', error);
            throw new Error(`Failed to stop server: ${error.message}`);
        }
    }

    async restartServer() {
        try {
            console.log('Restarting Minecraft server...');
            
            // Stop the server first
            await this.stopServer();
            
            // Wait a moment
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Start the server
            await this.startServer();
            
            return {
                success: true,
                message: 'Server restarted successfully',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error restarting server:', error);
            throw new Error(`Failed to restart server: ${error.message}`);
        }
    }

    async getServerStatus() {
        try {
            const container = await this.getContainer();
            
            if (!container) {
                return {
                    status: 'offline',
                    uptime: 0,
                    players: 0,
                    maxPlayers: 100,
                    version: 'Unknown',
                    memory: { used: 0, total: 0, percentage: 0 },
                    cpu: 0
                };
            }
            
            const containerInfo = await container.inspect();
            const isRunning = containerInfo.State.Running;
            
            if (!isRunning) {
                return {
                    status: 'offline',
                    uptime: 0,
                    players: 0,
                    maxPlayers: 100,
                    version: 'Unknown',
                    memory: { used: 0, total: 0, percentage: 0 },
                    cpu: 0
                };
            }
            
            // Calculate uptime
            const startTime = new Date(containerInfo.State.StartedAt);
            const uptime = Date.now() - startTime.getTime();
            
            // Get container stats
            const stats = await this.getContainerStats(container);
            
            // Get server info from server.properties
            const serverInfo = await this.getServerProperties();
            
            // Get online players (this would need RCON or log parsing)
            const playerInfo = await this.getPlayerInfo();
            
            return {
                status: 'online',
                uptime: uptime,
                players: playerInfo.online,
                maxPlayers: serverInfo.maxPlayers || 100,
                version: serverInfo.version || 'Paper 1.20.4',
                memory: stats.memory,
                cpu: stats.cpu,
                port: serverInfo.port || 25565,
                difficulty: serverInfo.difficulty || 'hard',
                gamemode: serverInfo.gamemode || 'survival'
            };
        } catch (error) {
            console.error('Error getting server status:', error);
            return {
                status: 'unknown',
                error: error.message,
                uptime: 0,
                players: 0,
                maxPlayers: 100
            };
        }
    }

    async getContainer() {
        try {
            const containers = await this.docker.listContainers({ all: true });
            const container = containers.find(c => 
                c.Names.some(name => name.includes(this.containerName))
            );
            
            if (container) {
                return this.docker.getContainer(container.Id);
            }
            
            return null;
        } catch (error) {
            console.error('Error getting container:', error);
            return null;
        }
    }

    async getContainerStats(container) {
        try {
            const stats = await container.stats({ stream: false });
            
            // Calculate memory usage
            const memoryUsage = stats.memory_stats.usage || 0;
            const memoryLimit = stats.memory_stats.limit || 0;
            const memoryPercentage = memoryLimit > 0 ? (memoryUsage / memoryLimit) * 100 : 0;
            
            // Calculate CPU usage
            const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - 
                           (stats.precpu_stats.cpu_usage?.total_usage || 0);
            const systemDelta = stats.cpu_stats.system_cpu_usage - 
                              (stats.precpu_stats.system_cpu_usage || 0);
            const cpuPercentage = systemDelta > 0 ? (cpuDelta / systemDelta) * 100 : 0;
            
            return {
                memory: {
                    used: memoryUsage,
                    total: memoryLimit,
                    percentage: Math.round(memoryPercentage)
                },
                cpu: Math.round(cpuPercentage)
            };
        } catch (error) {
            console.error('Error getting container stats:', error);
            return {
                memory: { used: 0, total: 0, percentage: 0 },
                cpu: 0
            };
        }
    }

    async getServerProperties() {
        try {
            const propertiesPath = path.join(this.serverPath, 'server.properties');
            const content = await fs.readFile(propertiesPath, 'utf8');
            
            const properties = {};
            const lines = content.split('\n');
            
            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed && !trimmed.startsWith('#')) {
                    const [key, ...valueParts] = trimmed.split('=');
                    if (key && valueParts.length > 0) {
                        properties[key.trim()] = valueParts.join('=').trim();
                    }
                }
            }
            
            return {
                maxPlayers: parseInt(properties['max-players']) || 100,
                port: parseInt(properties['server-port']) || 25565,
                difficulty: properties.difficulty || 'hard',
                gamemode: properties.gamemode || 'survival',
                version: 'Paper 1.20.4' // This would need to be detected from server jar
            };
        } catch (error) {
            console.error('Error reading server properties:', error);
            return {
                maxPlayers: 100,
                port: 25565,
                difficulty: 'hard',
                gamemode: 'survival',
                version: 'Paper 1.20.4'
            };
        }
    }

    async getPlayerInfo() {
        try {
            // This would typically use RCON or parse the latest.log file
            // For now, return mock data
            return {
                online: 0,
                players: []
            };
        } catch (error) {
            console.error('Error getting player info:', error);
            return {
                online: 0,
                players: []
            };
        }
    }

    async executeServerCommand(command) {
        try {
            const container = await this.getContainer();
            if (!container) {
                throw new Error('Server container not found');
            }
            
            const containerInfo = await container.inspect();
            if (!containerInfo.State.Running) {
                throw new Error('Server is not running');
            }
            
            // Execute command in the container
            const exec = await container.exec({
                Cmd: ['sh', '-c', `echo "${command}" > /proc/1/fd/0`],
                AttachStdout: true,
                AttachStderr: true
            });
            
            const stream = await exec.start();
            
            return new Promise((resolve, reject) => {
                let output = '';
                
                stream.on('data', (data) => {
                    output += data.toString();
                });
                
                stream.on('end', () => {
                    resolve(output);
                });
                
                stream.on('error', (error) => {
                    reject(error);
                });
                
                setTimeout(() => {
                    reject(new Error('Command timeout'));
                }, 10000);
            });
        } catch (error) {
            console.error('Error executing server command:', error);
            throw error;
        }
    }

    async getOnlinePlayers() {
        try {
            // This would typically use RCON to get the player list
            // For now, return mock data
            return [];
        } catch (error) {
            console.error('Error getting online players:', error);
            return [];
        }
    }

    async kickPlayer(playerName, reason = 'Kicked by admin') {
        try {
            await this.executeServerCommand(`kick ${playerName} ${reason}`);
            return { success: true, message: `Player ${playerName} kicked` };
        } catch (error) {
            throw new Error(`Failed to kick player: ${error.message}`);
        }
    }

    async banPlayer(playerName, reason = 'Banned by admin') {
        try {
            await this.executeServerCommand(`ban ${playerName} ${reason}`);
            return { success: true, message: `Player ${playerName} banned` };
        } catch (error) {
            throw new Error(`Failed to ban player: ${error.message}`);
        }
    }

    async unbanPlayer(playerName) {
        try {
            await this.executeServerCommand(`pardon ${playerName}`);
            return { success: true, message: `Player ${playerName} unbanned` };
        } catch (error) {
            throw new Error(`Failed to unban player: ${error.message}`);
        }
    }

    async getServerStats() {
        try {
            const status = await this.getServerStatus();

            // Generate some historical data for charts
            const now = new Date();
            const stats = {
                timestamp: now.toISOString(),
                cpu: status.cpu || 0,
                memory: status.memory?.percentage || 0,
                players: status.players || 0,
                uptime: status.uptime || 0
            };

            return stats;
        } catch (error) {
            console.error('Error getting server stats:', error);
            return null;
        }
    }

    async getPlugins() {
        try {
            const pluginsPath = path.join(this.serverPath, 'plugins');
            const files = await fs.readdir(pluginsPath);

            const plugins = [];

            for (const file of files) {
                if (file.endsWith('.jar')) {
                    const pluginPath = path.join(pluginsPath, file);
                    const stats = await fs.stat(pluginPath);

                    plugins.push({
                        name: file.replace('.jar', ''),
                        filename: file,
                        size: stats.size,
                        modified: stats.mtime.toISOString(),
                        enabled: true, // This would need to be determined from server state
                        version: 'Unknown' // This would need to be extracted from plugin.yml
                    });
                }
            }

            return plugins;
        } catch (error) {
            console.error('Error getting plugins:', error);
            return [];
        }
    }

    async enablePlugin(pluginName) {
        try {
            await this.executeServerCommand(`plugman enable ${pluginName}`);
            return { success: true, message: `Plugin ${pluginName} enabled` };
        } catch (error) {
            throw new Error(`Failed to enable plugin: ${error.message}`);
        }
    }

    async disablePlugin(pluginName) {
        try {
            await this.executeServerCommand(`plugman disable ${pluginName}`);
            return { success: true, message: `Plugin ${pluginName} disabled` };
        } catch (error) {
            throw new Error(`Failed to disable plugin: ${error.message}`);
        }
    }

    async getPluginConfig(pluginName) {
        try {
            const configPath = path.join(this.serverPath, 'plugins', pluginName, 'config.yml');
            const content = await fs.readFile(configPath, 'utf8');
            return { content, path: configPath };
        } catch (error) {
            throw new Error(`Failed to read plugin config: ${error.message}`);
        }
    }

    async updatePluginConfig(pluginName, config) {
        try {
            const configPath = path.join(this.serverPath, 'plugins', pluginName, 'config.yml');
            await fs.writeFile(configPath, config);
            return { success: true, message: 'Plugin configuration updated' };
        } catch (error) {
            throw new Error(`Failed to update plugin config: ${error.message}`);
        }
    }

    async updateServerProperties(properties) {
        try {
            const propertiesPath = path.join(this.serverPath, 'server.properties');

            // Convert properties object to server.properties format
            let content = '# Minecraft server properties\n';
            content += `# Updated on ${new Date().toISOString()}\n\n`;

            for (const [key, value] of Object.entries(properties)) {
                content += `${key}=${value}\n`;
            }

            await fs.writeFile(propertiesPath, content);
            return { success: true, message: 'Server properties updated' };
        } catch (error) {
            throw new Error(`Failed to update server properties: ${error.message}`);
        }
    }

    async readFile(filePath) {
        try {
            // Security check - ensure file is within server directory
            const resolvedPath = path.resolve(filePath);
            const resolvedServerPath = path.resolve(this.serverPath);

            if (!resolvedPath.startsWith(resolvedServerPath)) {
                throw new Error('Access denied: File outside server directory');
            }

            const content = await fs.readFile(resolvedPath, 'utf8');
            return { content, path: filePath };
        } catch (error) {
            throw new Error(`Failed to read file: ${error.message}`);
        }
    }

    async writeFile(filePath, content) {
        try {
            // Security check - ensure file is within server directory
            const resolvedPath = path.resolve(filePath);
            const resolvedServerPath = path.resolve(this.serverPath);

            if (!resolvedPath.startsWith(resolvedServerPath)) {
                throw new Error('Access denied: File outside server directory');
            }

            await fs.writeFile(resolvedPath, content);
            return { success: true, message: 'File saved successfully' };
        } catch (error) {
            throw new Error(`Failed to write file: ${error.message}`);
        }
    }

    async getDetailedStats() {
        try {
            const container = await this.getContainer();
            if (!container) {
                return null;
            }

            const containerInfo = await container.inspect();
            const stats = await this.getContainerStats(container);

            // Get network stats
            const networkStats = containerInfo.NetworkSettings || {};

            // Get detailed system information
            const detailedStats = {
                container: {
                    id: containerInfo.Id.substring(0, 12),
                    name: containerInfo.Name,
                    status: containerInfo.State.Status,
                    startedAt: containerInfo.State.StartedAt,
                    restartCount: containerInfo.RestartCount
                },
                resources: {
                    cpu: stats.cpu,
                    memory: stats.memory,
                    network: {
                        rxBytes: networkStats.RxBytes || 0,
                        txBytes: networkStats.TxBytes || 0
                    }
                },
                server: await this.getServerProperties(),
                timestamp: new Date().toISOString()
            };

            return detailedStats;
        } catch (error) {
            console.error('Error getting detailed stats:', error);
            return null;
        }
    }

    async getHistoricalStats(hours = 24) {
        try {
            // In a real implementation, this would read from a database or log files
            // For now, generate mock historical data
            const stats = [];
            const now = new Date();
            const interval = (hours * 60 * 60 * 1000) / 100; // 100 data points

            for (let i = 99; i >= 0; i--) {
                const timestamp = new Date(now.getTime() - i * interval);
                stats.push({
                    timestamp: timestamp.toISOString(),
                    cpu: Math.random() * 80 + 10, // 10-90%
                    memory: Math.random() * 60 + 30, // 30-90%
                    players: Math.floor(Math.random() * 20), // 0-20 players
                    tps: Math.random() * 5 + 15 // 15-20 TPS
                });
            }

            return stats;
        } catch (error) {
            console.error('Error getting historical stats:', error);
            return [];
        }
    }
}

module.exports = ServerService;

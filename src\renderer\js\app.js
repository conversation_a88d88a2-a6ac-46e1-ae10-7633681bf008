// Main Application Controller
class MinecraftDashboard {
    constructor() {
        this.currentPage = 'dashboard';
        this.serverStatus = 'offline';
        this.updateInterval = null;
        this.init();
    }

    async init() {
        try {
            // Initialize UI components
            this.initSidebar();
            this.initNavigation();
            this.initMenuHandlers();
            this.initNotifications();

            // Initialize notification center if available
            if (typeof NotificationCenter !== 'undefined') {
                window.notificationCenter = new NotificationCenter();
            }

            // Load initial page
            await this.loadPage('dashboard');

            // Start periodic updates
            this.startPeriodicUpdates();

            // Hide loading overlay
            this.hideLoading();

            console.log('Minecraft Dashboard initialized successfully');
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            this.showNotification('Error', 'Failed to initialize dashboard', 'error');
            // Still hide loading even if there's an error
            this.hideLoading();
        }
    }

    initSidebar() {
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }

        // Handle responsive sidebar
        if (window.innerWidth <= 768) {
            sidebar?.classList.add('collapsed');
        }

        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                sidebar?.classList.add('collapsed');
            } else {
                sidebar?.classList.remove('collapsed');
            }
        });
    }

    initNavigation() {
        const menuItems = document.querySelectorAll('.menu-item');
        
        menuItems.forEach(item => {
            item.addEventListener('click', async (e) => {
                e.preventDefault();
                
                const page = item.getAttribute('data-page');
                if (page && page !== this.currentPage) {
                    await this.loadPage(page);
                }
            });
        });
    }

    initMenuHandlers() {
        // Listen for menu actions from main process
        if (window.electronAPI) {
            window.electronAPI.onMenuAction((event, action, data) => {
                this.handleMenuAction(action, data);
            });
        }
    }

    initNotifications() {
        // Listen for notifications from main process
        if (window.electronAPI) {
            window.electronAPI.onNotification((event, notification) => {
                this.showNotification(
                    notification.title,
                    notification.message,
                    notification.type
                );
            });
        }
    }

    async loadPage(pageName) {
        try {
            // Update active menu item
            this.updateActiveMenuItem(pageName);

            // Update page title and breadcrumb
            this.updatePageHeader(pageName);

            // Load page content
            const contentArea = document.getElementById('content-area');
            if (contentArea) {
                // Show loading state
                contentArea.innerHTML = '<div class="loading-spinner"></div>';

                // Add timeout to prevent infinite loading
                const loadPageWithTimeout = async () => {
                    return Promise.race([
                        this.loadPageContent(pageName, contentArea),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Page load timeout')), 10000)
                        )
                    ]);
                };

                await loadPageWithTimeout();
                this.currentPage = pageName;
            }
        } catch (error) {
            console.error(`Failed to load page ${pageName}:`, error);
            this.showNotification('Error', `Failed to load ${pageName} page`, 'error');

            // Show error page instead of infinite loading
            const contentArea = document.getElementById('content-area');
            if (contentArea) {
                contentArea.innerHTML = `
                    <div class="alert alert-error">
                        <h3>Failed to load ${pageName} page</h3>
                        <p>${error.message}</p>
                        <button class="btn btn-primary" onclick="window.app.loadPage('${pageName}')">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </div>
                `;
            }
        }
    }

    async loadPageContent(pageName, contentArea) {
        // Load page-specific content
        switch (pageName) {
            case 'dashboard':
                await DashboardPage.render(contentArea);
                break;
            case 'server-control':
                await ServerControlPage.render(contentArea);
                break;
            case 'players':
                await PlayersPage.render(contentArea);
                break;
            case 'logs':
                await LogsPage.render(contentArea);
                break;
            case 'plugins':
                await PluginsPage.render(contentArea);
                break;
            case 'database':
                await DatabasePage.render(contentArea);
                break;
            case 'backups':
                await BackupsPage.render(contentArea);
                break;
            case 'settings':
                await SettingsPage.render(contentArea);
                break;
            case 'security':
                await SecurityPage.render(contentArea);
                break;
            default:
                contentArea.innerHTML = '<div class="alert alert-error">Page not found</div>';
        }
    }

    updateActiveMenuItem(pageName) {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-page') === pageName) {
                item.classList.add('active');
            }
        });
    }

    updatePageHeader(pageName) {
        const pageTitle = document.getElementById('page-title');
        const breadcrumbCurrent = document.getElementById('breadcrumb-current');
        
        const pageNames = {
            'dashboard': 'Dashboard',
            'server-control': 'Server Control',
            'players': 'Players',
            'logs': 'Server Logs',
            'plugins': 'Plugins',
            'database': 'Database',
            'backups': 'Backups',
            'settings': 'Server Settings',
            'security': 'Security'
        };
        
        const displayName = pageNames[pageName] || pageName;
        
        if (pageTitle) pageTitle.textContent = displayName;
        if (breadcrumbCurrent) breadcrumbCurrent.textContent = displayName;
    }

    async handleMenuAction(action, data) {
        switch (action) {
            case 'new-backup':
                await this.createBackup();
                break;
            case 'import-config':
                await this.importConfig(data);
                break;
            case 'start-server':
                await this.startServer();
                break;
            case 'stop-server':
                await this.stopServer();
                break;
            case 'restart-server':
                await this.restartServer();
                break;
            default:
                console.log('Unknown menu action:', action);
        }
    }

    async startServer() {
        try {
            this.showNotification('Server', 'Starting server...', 'info');
            if (window.electronAPI) {
                await window.electronAPI.startServer();
                this.updateServerStatus('starting');
            }
        } catch (error) {
            console.error('Failed to start server:', error);
            this.showNotification('Error', 'Failed to start server', 'error');
        }
    }

    async stopServer() {
        try {
            this.showNotification('Server', 'Stopping server...', 'info');
            if (window.electronAPI) {
                await window.electronAPI.stopServer();
                this.updateServerStatus('offline');
            }
        } catch (error) {
            console.error('Failed to stop server:', error);
            this.showNotification('Error', 'Failed to stop server', 'error');
        }
    }

    async restartServer() {
        try {
            this.showNotification('Server', 'Restarting server...', 'info');
            if (window.electronAPI) {
                await window.electronAPI.restartServer();
                this.updateServerStatus('starting');
            }
        } catch (error) {
            console.error('Failed to restart server:', error);
            this.showNotification('Error', 'Failed to restart server', 'error');
        }
    }

    async createBackup() {
        try {
            const name = prompt('Enter backup name:');
            if (name) {
                this.showNotification('Backup', 'Creating backup...', 'info');
                if (window.electronAPI) {
                    await window.electronAPI.createBackup(name, 'Manual backup');
                    this.showNotification('Success', 'Backup created successfully', 'success');
                }
            }
        } catch (error) {
            console.error('Failed to create backup:', error);
            this.showNotification('Error', 'Failed to create backup', 'error');
        }
    }

    async importConfig(filePath) {
        try {
            this.showNotification('Config', 'Importing configuration...', 'info');
            // Implementation for config import
            this.showNotification('Success', 'Configuration imported successfully', 'success');
        } catch (error) {
            console.error('Failed to import config:', error);
            this.showNotification('Error', 'Failed to import configuration', 'error');
        }
    }

    updateServerStatus(status) {
        this.serverStatus = status;
        const serverStatusElement = document.getElementById('server-status');
        
        if (serverStatusElement) {
            const indicator = serverStatusElement.querySelector('.status-indicator');
            const text = serverStatusElement.querySelector('span');
            
            indicator.className = `status-indicator ${status}`;
            
            switch (status) {
                case 'online':
                    text.textContent = 'Server Online';
                    break;
                case 'offline':
                    text.textContent = 'Server Offline';
                    break;
                case 'starting':
                    text.textContent = 'Server Starting';
                    break;
                default:
                    text.textContent = 'Unknown Status';
            }
        }
    }

    startPeriodicUpdates() {
        // Update server status immediately
        this.updateServerStatusFromAPI();

        // Update server status every 30 seconds
        this.updateInterval = setInterval(async () => {
            this.updateServerStatusFromAPI();
        }, 30000);
    }

    async updateServerStatusFromAPI() {
        try {
            if (window.electronAPI) {
                const statusData = await window.electronAPI.getServerStatus();
                // statusData is an object with status property
                this.updateServerStatus(statusData.status || 'unknown');
            }
        } catch (error) {
            console.error('Failed to update server status:', error);
            this.updateServerStatus('unknown');
        }
    }

    showNotification(title, message, type = 'info') {
        const notification = new ToastNotification(title, message, type);
        notification.show();
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        if (window.electronAPI) {
            window.electronAPI.removeMenuActionListener();
            window.electronAPI.removeNotificationListener();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MinecraftDashboard();
});

// Handle app cleanup
window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.destroy();
    }
});

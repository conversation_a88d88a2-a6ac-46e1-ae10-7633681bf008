const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const axios = require('axios');
const yaml = require('js-yaml');

const execAsync = promisify(exec);

class PluginService {
    constructor() {
        this.serverPath = path.join(process.cwd(), 'simMC');
        this.pluginsPath = path.join(this.serverPath, 'plugins');
        this.pluginDataPath = path.join(this.pluginsPath, 'plugin-data.json');
        
        this.initializePluginData();
    }

    async initializePluginData() {
        try {
            await fs.mkdir(this.pluginsPath, { recursive: true });
            
            // Create plugin data file if it doesn't exist
            try {
                await fs.access(this.pluginDataPath);
            } catch (error) {
                const initialData = {
                    plugins: {},
                    lastUpdated: new Date().toISOString()
                };
                await fs.writeFile(this.pluginDataPath, JSON.stringify(initialData, null, 2));
            }
            
            console.log('Plugin service initialized');
        } catch (error) {
            console.error('Failed to initialize plugin service:', error);
        }
    }

    async getInstalledPlugins() {
        try {
            const files = await fs.readdir(this.pluginsPath);
            const plugins = [];
            
            for (const file of files) {
                if (file.endsWith('.jar')) {
                    const pluginPath = path.join(this.pluginsPath, file);
                    const stats = await fs.stat(pluginPath);
                    
                    // Try to get plugin info from plugin.yml
                    const pluginInfo = await this.getPluginInfo(file);
                    
                    plugins.push({
                        id: file.replace('.jar', ''),
                        name: pluginInfo.name || file.replace('.jar', ''),
                        version: pluginInfo.version || 'Unknown',
                        description: pluginInfo.description || 'No description available',
                        author: pluginInfo.author || pluginInfo.authors?.join(', ') || 'Unknown',
                        fileName: file,
                        size: stats.size,
                        lastModified: stats.mtime,
                        enabled: await this.isPluginEnabled(file),
                        hasConfig: await this.hasPluginConfig(file.replace('.jar', '')),
                        dependencies: pluginInfo.depend || [],
                        softDependencies: pluginInfo.softdepend || []
                    });
                }
            }
            
            return plugins.sort((a, b) => a.name.localeCompare(b.name));
        } catch (error) {
            console.error('Error getting installed plugins:', error);
            return [];
        }
    }

    async getPluginInfo(jarFileName) {
        try {
            // Extract plugin.yml from JAR file
            const jarPath = path.join(this.pluginsPath, jarFileName);
            const tempDir = path.join(this.pluginsPath, 'temp_' + Date.now());
            
            await fs.mkdir(tempDir, { recursive: true });
            
            try {
                // Use unzip to extract plugin.yml
                await execAsync(`cd "${tempDir}" && jar -xf "${jarPath}" plugin.yml`);
                
                const pluginYmlPath = path.join(tempDir, 'plugin.yml');
                const pluginYmlContent = await fs.readFile(pluginYmlPath, 'utf8');
                const pluginInfo = yaml.load(pluginYmlContent);
                
                return pluginInfo;
            } finally {
                // Clean up temp directory
                await this.removeDirectory(tempDir);
            }
        } catch (error) {
            console.error(`Error reading plugin info for ${jarFileName}:`, error);
            return {};
        }
    }

    async isPluginEnabled(jarFileName) {
        try {
            // Check if plugin is in disabled plugins list or if .jar.disabled exists
            const disabledPath = path.join(this.pluginsPath, jarFileName + '.disabled');
            
            try {
                await fs.access(disabledPath);
                return false; // .disabled file exists
            } catch (error) {
                return true; // No .disabled file, plugin is enabled
            }
        } catch (error) {
            return true; // Default to enabled if we can't determine
        }
    }

    async enablePlugin(pluginId) {
        try {
            const jarFileName = pluginId + '.jar';
            const disabledPath = path.join(this.pluginsPath, jarFileName + '.disabled');
            const enabledPath = path.join(this.pluginsPath, jarFileName);
            
            // Check if disabled version exists
            try {
                await fs.access(disabledPath);
                await fs.rename(disabledPath, enabledPath);
                
                return {
                    success: true,
                    message: `Plugin ${pluginId} enabled successfully`
                };
            } catch (error) {
                // Plugin might already be enabled
                try {
                    await fs.access(enabledPath);
                    return {
                        success: true,
                        message: `Plugin ${pluginId} is already enabled`
                    };
                } catch (error) {
                    throw new Error(`Plugin ${pluginId} not found`);
                }
            }
        } catch (error) {
            console.error(`Error enabling plugin ${pluginId}:`, error);
            throw new Error(`Failed to enable plugin: ${error.message}`);
        }
    }

    async disablePlugin(pluginId) {
        try {
            const jarFileName = pluginId + '.jar';
            const enabledPath = path.join(this.pluginsPath, jarFileName);
            const disabledPath = path.join(this.pluginsPath, jarFileName + '.disabled');
            
            // Check if enabled version exists
            try {
                await fs.access(enabledPath);
                await fs.rename(enabledPath, disabledPath);
                
                return {
                    success: true,
                    message: `Plugin ${pluginId} disabled successfully`
                };
            } catch (error) {
                throw new Error(`Plugin ${pluginId} not found or already disabled`);
            }
        } catch (error) {
            console.error(`Error disabling plugin ${pluginId}:`, error);
            throw new Error(`Failed to disable plugin: ${error.message}`);
        }
    }

    async removeDirectory(dirPath) {
        try {
            const files = await fs.readdir(dirPath);

            for (const file of files) {
                const filePath = path.join(dirPath, file);
                const stats = await fs.stat(filePath);

                if (stats.isDirectory()) {
                    await this.removeDirectory(filePath);
                } else {
                    await fs.unlink(filePath);
                }
            }

            await fs.rmdir(dirPath);
        } catch (error) {
            console.error('Error removing directory:', error);
        }
    }

    async hasPluginConfig(pluginName) {
        try {
            const configPath = path.join(this.pluginsPath, pluginName, 'config.yml');
            await fs.access(configPath);
            return true;
        } catch (error) {
            return false;
        }
    }

    async getPluginConfig(pluginName) {
        try {
            const configPath = path.join(this.pluginsPath, pluginName, 'config.yml');
            const configContent = await fs.readFile(configPath, 'utf8');
            return {
                success: true,
                config: configContent,
                parsed: yaml.load(configContent)
            };
        } catch (error) {
            console.error(`Error reading plugin config for ${pluginName}:`, error);
            throw new Error(`Failed to read plugin configuration: ${error.message}`);
        }
    }

    async updatePluginConfig(pluginName, configContent) {
        try {
            const configPath = path.join(this.pluginsPath, pluginName, 'config.yml');

            // Validate YAML syntax
            try {
                yaml.load(configContent);
            } catch (yamlError) {
                throw new Error(`Invalid YAML syntax: ${yamlError.message}`);
            }

            // Create backup of current config
            const backupPath = path.join(this.pluginsPath, pluginName, `config.yml.backup.${Date.now()}`);
            try {
                await fs.copyFile(configPath, backupPath);
            } catch (error) {
                // Backup failed, but continue with update
                console.warn(`Failed to create config backup for ${pluginName}:`, error);
            }

            // Write new config
            await fs.writeFile(configPath, configContent, 'utf8');

            return {
                success: true,
                message: `Configuration for ${pluginName} updated successfully`
            };
        } catch (error) {
            console.error(`Error updating plugin config for ${pluginName}:`, error);
            throw new Error(`Failed to update plugin configuration: ${error.message}`);
        }
    }

    async deletePlugin(pluginId) {
        try {
            const jarFileName = pluginId + '.jar';
            const enabledPath = path.join(this.pluginsPath, jarFileName);
            const disabledPath = path.join(this.pluginsPath, jarFileName + '.disabled');
            const pluginDataPath = path.join(this.pluginsPath, pluginId);

            // Remove JAR file (enabled or disabled)
            try {
                await fs.unlink(enabledPath);
            } catch (error) {
                try {
                    await fs.unlink(disabledPath);
                } catch (error) {
                    throw new Error(`Plugin ${pluginId} not found`);
                }
            }

            // Remove plugin data directory if it exists
            try {
                await this.removeDirectory(pluginDataPath);
            } catch (error) {
                // Plugin data directory might not exist, that's okay
            }

            return {
                success: true,
                message: `Plugin ${pluginId} deleted successfully`
            };
        } catch (error) {
            console.error(`Error deleting plugin ${pluginId}:`, error);
            throw new Error(`Failed to delete plugin: ${error.message}`);
        }
    }

    async installPlugin(pluginFile, pluginData) {
        try {
            const fileName = pluginData.fileName || `${pluginData.name}.jar`;
            const targetPath = path.join(this.pluginsPath, fileName);

            // Copy plugin file to plugins directory
            await fs.copyFile(pluginFile, targetPath);

            // Update plugin data
            await this.updatePluginData(pluginData);

            return {
                success: true,
                message: `Plugin ${pluginData.name} installed successfully`,
                plugin: pluginData
            };
        } catch (error) {
            console.error('Error installing plugin:', error);
            throw new Error(`Failed to install plugin: ${error.message}`);
        }
    }

    async updatePluginData(pluginData) {
        try {
            let data = {};

            try {
                const content = await fs.readFile(this.pluginDataPath, 'utf8');
                data = JSON.parse(content);
            } catch (error) {
                // File doesn't exist or is invalid, start with empty data
                data = { plugins: {}, lastUpdated: new Date().toISOString() };
            }

            data.plugins[pluginData.id] = {
                ...pluginData,
                installedAt: new Date().toISOString()
            };
            data.lastUpdated = new Date().toISOString();

            await fs.writeFile(this.pluginDataPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Error updating plugin data:', error);
        }
    }

    async reloadPlugins() {
        try {
            // This would typically send a reload command to the server
            // For now, return a placeholder response
            return {
                success: true,
                message: 'Plugin reload command sent (requires server restart for full effect)'
            };
        } catch (error) {
            console.error('Error reloading plugins:', error);
            throw new Error(`Failed to reload plugins: ${error.message}`);
        }
    }
}

module.exports = PluginService;

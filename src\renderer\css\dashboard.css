/* Dashboard Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dashboard-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

/* Stat Cards */
.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-card-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.stat-card-icon.primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.stat-card-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stat-card-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stat-card-icon.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.stat-card-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-card-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;
    font-weight: 500;
}

.stat-card-change.positive {
    color: var(--success-color);
}

.stat-card-change.negative {
    color: var(--error-color);
}

.stat-card-change.neutral {
    color: var(--text-secondary);
}

/* Chart Container */
.chart-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-control {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.chart-control.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-wrapper {
    position: relative;
    height: 300px;
}

/* Activity Feed */
.activity-feed {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.activity-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.activity-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
    margin-top: 2px;
}

.activity-icon.info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.activity-icon.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.activity-icon.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.activity-icon.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.activity-content {
    flex: 1;
}

.activity-message {
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-time {
    font-size: 12px;
    color: var(--text-secondary);
}

/* Server Status Panel */
.server-status-panel {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.server-status-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.server-status-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.server-status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.server-status-badge.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.server-status-badge.offline {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.server-status-badge.starting {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.server-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.server-info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.server-info-label {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.server-info-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.server-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Quick Actions */
.quick-actions {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.quick-actions-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-action:hover {
    background: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    background: var(--primary-color);
    color: white;
}

.quick-action-label {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-width: 400px;
}

.toast {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    animation: slideIn 0.3s ease-out;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.info {
    border-left: 4px solid var(--info-color);
}

.toast-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.toast-message {
    font-size: 13px;
    color: var(--text-secondary);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color var(--transition-fast);
}

.toast-close:hover {
    background: var(--gray-100);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: background-color var(--transition-fast);
}

.modal-close:hover {
    background: var(--gray-100);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* Database Page Styles */
.tables-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.table-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.table-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.table-info {
    flex: 1;
}

.table-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.table-name i {
    color: var(--primary-color);
}

.table-details {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.table-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.table-viewer-controls {
    background: var(--bg-tertiary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.query-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.query-result-table {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-text {
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
}

/* Performance Metrics */
.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.metric-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.metric-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.metric-value {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: right;
}

.performance-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

/* Container Details */
.container-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-item:last-child {
    border-bottom: none;
}

.container-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    flex-wrap: wrap;
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-group .btn {
    border-radius: var(--radius-sm);
}

/* Skyblock Actions */
.skyblock-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* Log Viewer Styles */
.log-controls {
    margin-bottom: var(--spacing-lg);
}

.log-filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.log-viewer {
    height: 500px;
    background: #1a1a1a;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-container {
    padding: var(--spacing-sm);
}

.log-entry {
    display: grid;
    grid-template-columns: 80px 60px 120px 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
    transition: background-color var(--transition-fast);
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.05);
}

.log-entry.log-error {
    border-left: 3px solid var(--error-color);
    background: rgba(239, 68, 68, 0.1);
}

.log-entry.log-warning {
    border-left: 3px solid var(--warning-color);
    background: rgba(245, 158, 11, 0.1);
}

.log-entry.log-info {
    border-left: 3px solid var(--info-color);
}

.log-entry.log-debug {
    border-left: 3px solid var(--gray-500);
    opacity: 0.7;
}

.log-time {
    color: var(--gray-400);
    font-weight: 500;
}

.log-level-badge {
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.log-level-error {
    background: var(--error-color);
    color: white;
}

.log-level-warning {
    background: var(--warning-color);
    color: white;
}

.log-level-info {
    background: var(--info-color);
    color: white;
}

.log-level-debug {
    background: var(--gray-500);
    color: white;
}

.log-thread {
    color: var(--gray-400);
    font-size: 11px;
}

.log-message {
    color: #ffffff;
    word-break: break-word;
}

.log-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-secondary);
}

/* Log Files List */
.log-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.log-file-item:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.file-name i {
    color: var(--primary-color);
}

.file-details {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.file-date {
    font-size: 12px;
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: var(--spacing-xs);
}

/* Log Statistics */
.log-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .table-actions {
        align-self: stretch;
        justify-content: flex-end;
    }

    .performance-actions,
    .container-actions,
    .skyblock-actions {
        flex-direction: column;
    }

    .btn-group {
        justify-content: center;
    }

    .log-filters {
        grid-template-columns: 1fr;
    }

    .log-entry {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }

    .log-file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .file-actions {
        align-self: stretch;
        justify-content: flex-end;
    }
}

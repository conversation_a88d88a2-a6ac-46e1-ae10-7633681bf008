{"name": "buildcheck", "version": "0.0.6", "author": "<PERSON> <<EMAIL>>", "description": "Build environment checking (a la autoconf) for node.js", "main": "./lib/index.js", "engines": {"node": ">=10.0.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.0.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib", "lint:fix": "npm run lint -- --fix"}, "keywords": ["build", "autoconf", "addons", "compiler", "environment"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/buildcheck/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/buildcheck.git"}}
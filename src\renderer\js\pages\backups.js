// Backups Management Page
class BackupsPage {
    static async render(container) {
        const html = `
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Total Backups</div>
                        <div class="stat-card-icon primary">
                            <i class="fas fa-save"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="backups-total-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-archive"></i>
                        <span>Available</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Total Size</div>
                        <div class="stat-card-icon success">
                            <i class="fas fa-hdd"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="backups-total-size">0 GB</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-weight"></i>
                        <span>Storage used</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Latest Backup</div>
                        <div class="stat-card-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="backups-latest-time">Never</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-calendar"></i>
                        <span>Last created</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Auto Backup</div>
                        <div class="stat-card-icon error">
                            <i class="fas fa-robot"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="backups-auto-status">Disabled</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-cog"></i>
                        <span>Scheduled</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Create New Backup</h3>
                    <p class="card-subtitle">Create a backup of your server data</p>
                </div>
                <div class="card-body">
                    <div class="backup-form">
                        <div class="form-group">
                            <label class="form-label">Backup Name</label>
                            <input type="text" class="form-control" id="backup-name"
                                   placeholder="Enter backup name (optional)">
                            <div class="form-text">
                                If left empty, a timestamp will be used
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="backup-description" rows="3"
                                      placeholder="Enter backup description (optional)"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Backup Type</label>
                            <div class="backup-options">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" id="backup-full"
                                           name="backup-type" value="full" checked>
                                    <label class="form-check-label" for="backup-full">
                                        <strong>Full Backup</strong>
                                        <div class="option-description">
                                            Includes worlds, plugins, configurations, and player data
                                        </div>
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input type="radio" class="form-check-input" id="backup-worlds"
                                           name="backup-type" value="worlds">
                                    <label class="form-check-label" for="backup-worlds">
                                        <strong>Worlds Only</strong>
                                        <div class="option-description">
                                            Only world files and player data
                                        </div>
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input type="radio" class="form-check-input" id="backup-config"
                                           name="backup-type" value="config">
                                    <label class="form-check-label" for="backup-config">
                                        <strong>Configuration Only</strong>
                                        <div class="option-description">
                                            Server properties, plugin configs, and settings
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="backup-actions">
                            <button class="btn btn-primary" onclick="BackupsPage.createBackup()">
                                <i class="fas fa-save"></i>
                                Create Backup
                            </button>
                            <button class="btn btn-secondary" onclick="BackupsPage.scheduleBackup()">
                                <i class="fas fa-calendar-plus"></i>
                                Schedule Backup
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Backup History</h3>
                    <p class="card-subtitle">Manage existing backups</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-secondary" onclick="BackupsPage.cleanupOldBackups()">
                            <i class="fas fa-broom"></i>
                            Cleanup
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="BackupsPage.refreshBackups()">
                            <i class="fas fa-sync"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="backups-table"></div>
                </div>
            </div>

            <div class="dashboard-row">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Automatic Backups</h3>
                        <p class="card-subtitle">Configure scheduled backups</p>
                    </div>
                    <div class="card-body">
                        <div class="auto-backup-settings">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="auto-backup-enabled"
                                           onchange="BackupsPage.toggleAutoBackup()">
                                    <label class="form-check-label" for="auto-backup-enabled">
                                        Enable Automatic Backups
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Backup Frequency</label>
                                <select class="form-control" id="backup-frequency">
                                    <option value="hourly">Every Hour</option>
                                    <option value="daily" selected>Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Backup Time</label>
                                <input type="time" class="form-control" id="backup-time" value="03:00">
                                <div class="form-text">
                                    Time when daily/weekly backups should run
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Keep Backups</label>
                                <select class="form-control" id="backup-retention">
                                    <option value="7">7 days</option>
                                    <option value="14">14 days</option>
                                    <option value="30" selected>30 days</option>
                                    <option value="90">90 days</option>
                                    <option value="365">1 year</option>
                                </select>
                            </div>

                            <div class="auto-backup-actions">
                                <button class="btn btn-primary" onclick="BackupsPage.saveAutoBackupSettings()">
                                    <i class="fas fa-save"></i>
                                    Save Settings
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Backup Storage</h3>
                        <p class="card-subtitle">Storage information and cleanup</p>
                    </div>
                    <div class="card-body">
                        <div class="storage-info">
                            <div class="storage-item">
                                <div class="storage-label">Backup Directory</div>
                                <div class="storage-value">/backups</div>
                            </div>

                            <div class="storage-item">
                                <div class="storage-label">Available Space</div>
                                <div class="storage-value" id="storage-available">Calculating...</div>
                            </div>

                            <div class="storage-item">
                                <div class="storage-label">Used by Backups</div>
                                <div class="storage-value" id="storage-used">0 GB</div>
                            </div>

                            <div class="storage-progress">
                                <div class="storage-label">Storage Usage</div>
                                <div class="progress">
                                    <div class="progress-bar" id="storage-progress-bar" style="width: 0%"></div>
                                </div>
                                <div class="storage-percentage" id="storage-percentage">0%</div>
                            </div>
                        </div>

                        <div class="storage-actions">
                            <button class="btn btn-warning" onclick="BackupsPage.cleanupOldBackups()">
                                <i class="fas fa-trash"></i>
                                Cleanup Old Backups
                            </button>
                            <button class="btn btn-secondary" onclick="BackupsPage.openBackupFolder()">
                                <i class="fas fa-folder-open"></i>
                                Open Backup Folder
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Restore Backup Modal -->
            <div class="modal-overlay" id="restore-backup-modal">
                <div class="modal">
                    <div class="modal-header">
                        <h3 class="modal-title">Restore Backup</h3>
                        <button class="modal-close" onclick="BackupsPage.closeRestoreModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> Restoring a backup will overwrite current server data.
                            Make sure the server is stopped before proceeding.
                        </div>

                        <div class="backup-details" id="restore-backup-details">
                            <!-- Backup details will be populated here -->
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="create-pre-restore-backup">
                                <label class="form-check-label" for="create-pre-restore-backup">
                                    Create backup of current data before restoring
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="BackupsPage.closeRestoreModal()">
                            Cancel
                        </button>
                        <button class="btn btn-error" onclick="BackupsPage.confirmRestore()">
                            <i class="fas fa-undo"></i>
                            Restore Backup
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;

        // Initialize the page
        await this.initializePage();
    }

    static async initializePage() {
        this.selectedBackup = null;

        // Load initial data
        await this.loadBackupStats();
        await this.loadBackups();
        await this.loadStorageInfo();
        await this.loadAutoBackupSettings();
    }

    static async loadBackupStats() {
        try {
            // Mock backup statistics
            const stats = {
                totalBackups: 5,
                totalSize: '2.3 GB',
                latestBackup: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                autoBackupEnabled: false
            };

            document.getElementById('backups-total-count').textContent = stats.totalBackups;
            document.getElementById('backups-total-size').textContent = stats.totalSize;
            document.getElementById('backups-latest-time').textContent =
                stats.latestBackup ? formatRelativeTime(stats.latestBackup) : 'Never';
            document.getElementById('backups-auto-status').textContent =
                stats.autoBackupEnabled ? 'Enabled' : 'Disabled';

        } catch (error) {
            console.error('Error loading backup stats:', error);
        }
    }

    static async loadBackups() {
        try {
            const container = document.getElementById('backups-table');
            if (!container) return;

            // Mock backup data
            const backups = [
                {
                    id: 'backup_1',
                    name: 'manual_backup_2024-01-25',
                    description: 'Manual backup before plugin update',
                    created: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                    size: 456789123,
                    type: 'full',
                    status: 'completed'
                },
                {
                    id: 'backup_2',
                    name: 'auto_backup_2024-01-24',
                    description: 'Automatic daily backup',
                    created: new Date(Date.now() - 26 * 60 * 60 * 1000).toISOString(),
                    size: 445123789,
                    type: 'full',
                    status: 'completed'
                },
                {
                    id: 'backup_3',
                    name: 'worlds_backup_2024-01-23',
                    description: 'World files backup',
                    created: new Date(Date.now() - 50 * 60 * 60 * 1000).toISOString(),
                    size: 234567890,
                    type: 'worlds',
                    status: 'completed'
                }
            ];

            const table = new DataTable(container, {
                columns: [
                    { key: 'name', title: 'Backup Name', sortable: true },
                    { key: 'description', title: 'Description', sortable: false },
                    {
                        key: 'type',
                        title: 'Type',
                        sortable: true,
                        render: (value) => `<span class="badge badge-${value === 'full' ? 'primary' : 'secondary'}">${value}</span>`
                    },
                    {
                        key: 'size',
                        title: 'Size',
                        sortable: true,
                        render: (value) => formatBytes(value)
                    },
                    {
                        key: 'created',
                        title: 'Created',
                        sortable: true,
                        render: (value) => formatRelativeTime(value)
                    },
                    {
                        key: 'status',
                        title: 'Status',
                        sortable: true,
                        render: (value) => `<span class="badge badge-${value === 'completed' ? 'success' : 'warning'}">${value}</span>`
                    },
                    {
                        key: 'actions',
                        title: 'Actions',
                        render: (value, row) => `
                            <div class="btn-group">
                                <button class="btn btn-sm btn-primary" onclick="BackupsPage.showBackupDetails('${row.id}')" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="BackupsPage.restoreBackup('${row.id}')" title="Restore">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="BackupsPage.downloadBackup('${row.id}')" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn btn-sm btn-error" onclick="BackupsPage.deleteBackup('${row.id}')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `
                    }
                ],
                data: backups,
                searchable: true,
                pagination: true,
                pageSize: 10
            });

        } catch (error) {
            console.error('Error loading backups:', error);
        }
    }

    static async loadStorageInfo() {
        try {
            // Mock storage information
            const storageInfo = {
                available: '45.2 GB',
                used: '2.3 GB',
                percentage: 5
            };

            document.getElementById('storage-available').textContent = storageInfo.available;
            document.getElementById('storage-used').textContent = storageInfo.used;
            document.getElementById('storage-percentage').textContent = `${storageInfo.percentage}%`;

            const progressBar = document.getElementById('storage-progress-bar');
            if (progressBar) {
                progressBar.style.width = `${storageInfo.percentage}%`;

                // Change color based on usage
                if (storageInfo.percentage > 80) {
                    progressBar.className = 'progress-bar error';
                } else if (storageInfo.percentage > 60) {
                    progressBar.className = 'progress-bar warning';
                } else {
                    progressBar.className = 'progress-bar success';
                }
            }

        } catch (error) {
            console.error('Error loading storage info:', error);
        }
    }

    static async loadAutoBackupSettings() {
        try {
            // Mock auto backup settings
            const settings = {
                enabled: false,
                frequency: 'daily',
                time: '03:00',
                retention: 30
            };

            document.getElementById('auto-backup-enabled').checked = settings.enabled;
            document.getElementById('backup-frequency').value = settings.frequency;
            document.getElementById('backup-time').value = settings.time;
            document.getElementById('backup-retention').value = settings.retention;

        } catch (error) {
            console.error('Error loading auto backup settings:', error);
        }
    }

    static async createBackup() {
        try {
            const name = document.getElementById('backup-name').value.trim();
            const description = document.getElementById('backup-description').value.trim();
            const type = document.querySelector('input[name="backup-type"]:checked').value;

            const backupName = name || `${type}_backup_${new Date().toISOString().split('T')[0]}`;
            const backupDescription = description || `${type} backup created manually`;

            toastNotifications.show('Backup', 'Creating backup...', 'info');

            if (window.electronAPI) {
                await window.electronAPI.createBackup(backupName, backupDescription);
            }

            // Clear form
            document.getElementById('backup-name').value = '';
            document.getElementById('backup-description').value = '';

            // Refresh data
            await this.loadBackups();
            await this.loadBackupStats();
            await this.loadStorageInfo();

            toastNotifications.show('Success', 'Backup created successfully', 'success');

        } catch (error) {
            console.error('Error creating backup:', error);
            toastNotifications.show('Error', 'Failed to create backup: ' + error.message, 'error');
        }
    }

    static scheduleBackup() {
        toastNotifications.show('Info', 'Backup scheduling feature coming soon', 'info');
    }

    static restoreBackup(backupId) {
        this.selectedBackup = backupId;

        // Show restore modal with backup details
        const modal = document.getElementById('restore-backup-modal');
        const detailsContainer = document.getElementById('restore-backup-details');

        // Mock backup details
        detailsContainer.innerHTML = `
            <div class="backup-info">
                <div class="info-item">
                    <strong>Backup ID:</strong> ${backupId}
                </div>
                <div class="info-item">
                    <strong>Name:</strong> manual_backup_2024-01-25
                </div>
                <div class="info-item">
                    <strong>Size:</strong> 435 MB
                </div>
                <div class="info-item">
                    <strong>Created:</strong> 2 hours ago
                </div>
                <div class="info-item">
                    <strong>Type:</strong> Full Backup
                </div>
            </div>
        `;

        modal.classList.add('active');
    }

    static closeRestoreModal() {
        const modal = document.getElementById('restore-backup-modal');
        modal.classList.remove('active');
        this.selectedBackup = null;
    }

    static async confirmRestore() {
        if (!this.selectedBackup) return;

        try {
            const createPreBackup = document.getElementById('create-pre-restore-backup').checked;

            toastNotifications.show('Restore', 'Restoring backup...', 'info');

            if (window.electronAPI) {
                await window.electronAPI.restoreBackup(this.selectedBackup);
            }

            this.closeRestoreModal();
            toastNotifications.show('Success', 'Backup restored successfully', 'success');

        } catch (error) {
            console.error('Error restoring backup:', error);
            toastNotifications.show('Error', 'Failed to restore backup: ' + error.message, 'error');
        }
    }

    static async deleteBackup(backupId) {
        showConfirmDialog(
            'Delete Backup',
            'Are you sure you want to delete this backup? This action cannot be undone.',
            async () => {
                try {
                    if (window.electronAPI) {
                        await window.electronAPI.deleteBackup(backupId);
                    }

                    await this.loadBackups();
                    await this.loadBackupStats();
                    await this.loadStorageInfo();

                    toastNotifications.show('Success', 'Backup deleted successfully', 'success');
                } catch (error) {
                    console.error('Error deleting backup:', error);
                    toastNotifications.show('Error', 'Failed to delete backup: ' + error.message, 'error');
                }
            }
        );
    }

    static downloadBackup(backupId) {
        toastNotifications.show('Info', 'Downloading backup...', 'info');
        // Implementation would download the backup file
    }

    static showBackupDetails(backupId) {
        toastNotifications.show('Info', 'Backup details viewer coming soon', 'info');
    }

    static toggleAutoBackup() {
        const enabled = document.getElementById('auto-backup-enabled').checked;
        const statusElement = document.getElementById('backups-auto-status');

        if (statusElement) {
            statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
        }

        toastNotifications.show('Info',
            `Automatic backups ${enabled ? 'enabled' : 'disabled'}`,
            enabled ? 'success' : 'warning'
        );
    }

    static saveAutoBackupSettings() {
        const settings = {
            enabled: document.getElementById('auto-backup-enabled').checked,
            frequency: document.getElementById('backup-frequency').value,
            time: document.getElementById('backup-time').value,
            retention: document.getElementById('backup-retention').value
        };

        toastNotifications.show('Success', 'Auto backup settings saved', 'success');
        console.log('Auto backup settings:', settings);
    }

    static async refreshBackups() {
        await this.loadBackups();
        await this.loadBackupStats();
        await this.loadStorageInfo();
        toastNotifications.show('Success', 'Backup list refreshed', 'success');
    }

    static cleanupOldBackups() {
        showConfirmDialog(
            'Cleanup Old Backups',
            'This will delete backups older than the retention period. Continue?',
            () => {
                toastNotifications.show('Success', 'Old backups cleaned up', 'success');
                this.refreshBackups();
            }
        );
    }

    static openBackupFolder() {
        toastNotifications.show('Info', 'Opening backup folder...', 'info');
        // Implementation would open the backup folder in file explorer
    }
}

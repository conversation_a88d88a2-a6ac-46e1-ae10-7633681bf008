{
  "env": {
    "mocha": true,
    "node": true
  },
  "rules": {
    /* Possible Errors */
    "no-extra-parens": 2,
    "valid-jsdoc": 2,
    /* Best Practices */
    "block-scoped-var": 2,
    "complexity": [1, 3],
    "default-case": 2,
    "dot-notation": [2],
    "guard-for-in": 2,
    "no-div-regex": 2,
    "no-else-return": 2,
    "no-eq-null": 2,
    "no-floating-decimal": 2,
    "no-process-env": 2,
    "no-self-compare": 2,
    "no-void": 2,
    "no-warning-comments": 2,
    "radix": 2,
    "vars-on-top": 2,
    "wrap-iife": 2,
    /* Variables */
    "no-catch-shadow": 2,
    /* Node.JS */
    "no-sync": 2,
    /* Stylistic Issues */
    "no-mixed-spaces-and-tabs": 2,
    "no-underscore-dangle": 2,
    "quotes": [1, "single", "avoid-escape"]
}}

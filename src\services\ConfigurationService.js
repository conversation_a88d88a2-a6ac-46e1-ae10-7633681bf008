const fs = require('fs').promises;
const path = require('path');
const yaml = require('js-yaml');

class ConfigurationService {
    constructor() {
        this.serverPath = path.join(process.cwd(), 'simMC');
        this.configBackupPath = path.join(process.cwd(), 'config-backups');
        
        this.initializeService();
    }

    async initializeService() {
        try {
            await fs.mkdir(this.configBackupPath, { recursive: true });
            console.log('Configuration service initialized');
        } catch (error) {
            console.error('Failed to initialize configuration service:', error);
        }
    }

    // Server Properties Management
    async getServerProperties() {
        try {
            const propertiesPath = path.join(this.serverPath, 'server.properties');
            const content = await fs.readFile(propertiesPath, 'utf8');
            
            const properties = {};
            const lines = content.split('\n');
            
            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed && !trimmed.startsWith('#')) {
                    const [key, ...valueParts] = trimmed.split('=');
                    if (key && valueParts.length > 0) {
                        properties[key.trim()] = valueParts.join('=').trim();
                    }
                }
            }
            
            return {
                success: true,
                properties: properties,
                raw: content
            };
        } catch (error) {
            console.error('Error reading server properties:', error);
            throw new Error(`Failed to read server properties: ${error.message}`);
        }
    }

    async updateServerProperties(properties) {
        try {
            const propertiesPath = path.join(this.serverPath, 'server.properties');
            
            // Create backup before updating
            await this.createConfigBackup('server.properties');
            
            // Read current content to preserve comments
            let content = '';
            try {
                content = await fs.readFile(propertiesPath, 'utf8');
            } catch (error) {
                // File doesn't exist, create new one
                content = '# Minecraft server properties\n';
            }
            
            // Update properties while preserving comments
            const lines = content.split('\n');
            const updatedLines = [];
            const processedKeys = new Set();
            
            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed.startsWith('#') || !trimmed) {
                    // Keep comments and empty lines
                    updatedLines.push(line);
                } else {
                    const [key] = trimmed.split('=');
                    if (key && properties.hasOwnProperty(key.trim())) {
                        // Update existing property
                        updatedLines.push(`${key.trim()}=${properties[key.trim()]}`);
                        processedKeys.add(key.trim());
                    } else {
                        // Keep unchanged property
                        updatedLines.push(line);
                    }
                }
            }
            
            // Add new properties that weren't in the original file
            for (const [key, value] of Object.entries(properties)) {
                if (!processedKeys.has(key)) {
                    updatedLines.push(`${key}=${value}`);
                }
            }
            
            const newContent = updatedLines.join('\n');
            await fs.writeFile(propertiesPath, newContent, 'utf8');
            
            return {
                success: true,
                message: 'Server properties updated successfully'
            };
        } catch (error) {
            console.error('Error updating server properties:', error);
            throw new Error(`Failed to update server properties: ${error.message}`);
        }
    }

    // YAML Configuration Management
    async getYamlConfig(configPath) {
        try {
            const fullPath = path.join(this.serverPath, configPath);
            const content = await fs.readFile(fullPath, 'utf8');
            
            return {
                success: true,
                config: yaml.load(content),
                raw: content
            };
        } catch (error) {
            console.error(`Error reading YAML config ${configPath}:`, error);
            throw new Error(`Failed to read configuration: ${error.message}`);
        }
    }

    async updateYamlConfig(configPath, configData) {
        try {
            const fullPath = path.join(this.serverPath, configPath);
            
            // Create backup before updating
            await this.createConfigBackup(configPath);
            
            let content;
            if (typeof configData === 'string') {
                // Raw YAML content
                content = configData;
                // Validate YAML syntax
                yaml.load(content);
            } else {
                // Object to be converted to YAML
                content = yaml.dump(configData, {
                    indent: 2,
                    lineWidth: -1,
                    noRefs: true
                });
            }
            
            await fs.writeFile(fullPath, content, 'utf8');
            
            return {
                success: true,
                message: `Configuration ${configPath} updated successfully`
            };
        } catch (error) {
            console.error(`Error updating YAML config ${configPath}:`, error);
            throw new Error(`Failed to update configuration: ${error.message}`);
        }
    }

    // Configuration Backup Management
    async createConfigBackup(configFileName) {
        try {
            const sourcePath = path.join(this.serverPath, configFileName);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFileName = `${configFileName}.backup.${timestamp}`;
            const backupPath = path.join(this.configBackupPath, backupFileName);
            
            await fs.copyFile(sourcePath, backupPath);
            
            // Clean up old backups (keep only last 10)
            await this.cleanupOldBackups(configFileName);
            
            return {
                success: true,
                backupPath: backupPath,
                message: `Backup created for ${configFileName}`
            };
        } catch (error) {
            console.error(`Error creating config backup for ${configFileName}:`, error);
            throw new Error(`Failed to create backup: ${error.message}`);
        }
    }

    async getConfigBackups(configFileName) {
        try {
            const files = await fs.readdir(this.configBackupPath);
            const backups = [];
            
            for (const file of files) {
                if (file.startsWith(`${configFileName}.backup.`)) {
                    const filePath = path.join(this.configBackupPath, file);
                    const stats = await fs.stat(filePath);
                    
                    backups.push({
                        fileName: file,
                        path: filePath,
                        created: stats.mtime,
                        size: stats.size
                    });
                }
            }
            
            // Sort by creation date (newest first)
            backups.sort((a, b) => b.created - a.created);
            
            return backups;
        } catch (error) {
            console.error(`Error getting config backups for ${configFileName}:`, error);
            return [];
        }
    }

    async restoreConfigBackup(backupPath, configFileName) {
        try {
            const targetPath = path.join(this.serverPath, configFileName);
            
            // Create backup of current config before restoring
            await this.createConfigBackup(configFileName);
            
            // Restore from backup
            await fs.copyFile(backupPath, targetPath);
            
            return {
                success: true,
                message: `Configuration ${configFileName} restored from backup`
            };
        } catch (error) {
            console.error('Error restoring config backup:', error);
            throw new Error(`Failed to restore backup: ${error.message}`);
        }
    }

    async cleanupOldBackups(configFileName, maxBackups = 10) {
        try {
            const backups = await this.getConfigBackups(configFileName);
            
            if (backups.length > maxBackups) {
                const backupsToDelete = backups.slice(maxBackups);
                
                for (const backup of backupsToDelete) {
                    try {
                        await fs.unlink(backup.path);
                        console.log(`Cleaned up old backup: ${backup.fileName}`);
                    } catch (error) {
                        console.error(`Failed to cleanup backup ${backup.fileName}:`, error);
                    }
                }
            }
        } catch (error) {
            console.error('Error cleaning up old backups:', error);
        }
    }

    // Configuration Validation
    async validateConfiguration(configPath, configData) {
        try {
            const validationRules = this.getValidationRules(configPath);
            const errors = [];
            
            if (configPath.endsWith('.yml') || configPath.endsWith('.yaml')) {
                // Validate YAML syntax
                try {
                    if (typeof configData === 'string') {
                        yaml.load(configData);
                    }
                } catch (yamlError) {
                    errors.push(`Invalid YAML syntax: ${yamlError.message}`);
                }
            }
            
            // Apply specific validation rules
            if (validationRules) {
                const configObj = typeof configData === 'string' ? yaml.load(configData) : configData;
                this.applyValidationRules(configObj, validationRules, errors);
            }
            
            return {
                valid: errors.length === 0,
                errors: errors
            };
        } catch (error) {
            console.error('Error validating configuration:', error);
            return {
                valid: false,
                errors: [`Validation error: ${error.message}`]
            };
        }
    }

    getValidationRules(configPath) {
        // Define validation rules for different configuration files
        const rules = {
            'server.properties': {
                'server-port': { type: 'number', min: 1, max: 65535 },
                'max-players': { type: 'number', min: 1, max: 2147483647 },
                'difficulty': { type: 'string', values: ['peaceful', 'easy', 'normal', 'hard'] },
                'gamemode': { type: 'string', values: ['survival', 'creative', 'adventure', 'spectator'] }
            },
            'bukkit.yml': {
                'settings.allow-end': { type: 'boolean' },
                'settings.warn-on-overload': { type: 'boolean' },
                'spawn-limits.monsters': { type: 'number', min: 0 }
            }
        };
        
        return rules[configPath] || null;
    }

    applyValidationRules(configObj, rules, errors) {
        for (const [key, rule] of Object.entries(rules)) {
            const value = this.getNestedValue(configObj, key);
            
            if (value !== undefined) {
                if (rule.type === 'number' && typeof value !== 'number') {
                    errors.push(`${key} must be a number`);
                } else if (rule.type === 'boolean' && typeof value !== 'boolean') {
                    errors.push(`${key} must be a boolean`);
                } else if (rule.type === 'string' && typeof value !== 'string') {
                    errors.push(`${key} must be a string`);
                } else if (rule.values && !rule.values.includes(value)) {
                    errors.push(`${key} must be one of: ${rule.values.join(', ')}`);
                } else if (rule.min !== undefined && value < rule.min) {
                    errors.push(`${key} must be at least ${rule.min}`);
                } else if (rule.max !== undefined && value > rule.max) {
                    errors.push(`${key} must be at most ${rule.max}`);
                }
            }
        }
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }
}

module.exports = ConfigurationService;

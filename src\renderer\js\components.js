// UI Components

// Toast Notification Component
class ToastNotification {
    constructor(title, message, type = 'info', duration = 5000) {
        this.title = title;
        this.message = message;
        this.type = type;
        this.duration = duration;
        this.id = generateId();
    }

    show() {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = this.createElement();
        container.appendChild(toast);

        // Animate in
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        });

        // Auto remove
        if (this.duration > 0) {
            setTimeout(() => {
                this.remove();
            }, this.duration);
        }
    }

    createElement() {
        const toast = document.createElement('div');
        toast.className = `toast ${this.type}`;
        toast.id = this.id;
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        toast.style.transition = 'all 0.3s ease-out';

        const iconMap = {
            success: 'fas fa-check',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times',
            info: 'fas fa-info'
        };

        toast.innerHTML = `
            <div class="toast-icon ${this.type}">
                <i class="${iconMap[this.type] || iconMap.info}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${escapeHtml(this.title)}</div>
                <div class="toast-message">${escapeHtml(this.message)}</div>
            </div>
            <button class="toast-close" onclick="toastNotifications.remove('${this.id}')">
                <i class="fas fa-times"></i>
            </button>
        `;

        return toast;
    }

    remove() {
        const toast = document.getElementById(this.id);
        if (toast) {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }
}

// Toast Notifications Manager
const toastNotifications = {
    show(title, message, type = 'info', duration = 5000) {
        const notification = new ToastNotification(title, message, type, duration);
        notification.show();
        return notification;
    },

    remove(id) {
        const toast = document.getElementById(id);
        if (toast) {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }
};

// Modal Component
class Modal {
    constructor(title, content, options = {}) {
        this.title = title;
        this.content = content;
        this.options = {
            size: 'medium',
            closable: true,
            backdrop: true,
            ...options
        };
        this.id = generateId();
        this.isOpen = false;
    }

    show() {
        if (this.isOpen) return;

        const overlay = document.getElementById('modal-overlay');
        if (!overlay) return;

        const modal = this.createElement();
        overlay.innerHTML = '';
        overlay.appendChild(modal);
        overlay.classList.add('active');

        this.isOpen = true;

        // Handle backdrop click
        if (this.options.backdrop && this.options.closable) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.hide();
                }
            });
        }

        // Handle escape key
        if (this.options.closable) {
            document.addEventListener('keydown', this.handleEscape);
        }
    }

    hide() {
        if (!this.isOpen) return;

        const overlay = document.getElementById('modal-overlay');
        if (overlay) {
            overlay.classList.remove('active');
            setTimeout(() => {
                overlay.innerHTML = '';
            }, 300);
        }

        this.isOpen = false;
        document.removeEventListener('keydown', this.handleEscape);
    }

    handleEscape = (e) => {
        if (e.key === 'Escape' && this.isOpen) {
            this.hide();
        }
    }

    createElement() {
        const modal = document.createElement('div');
        modal.className = `modal modal-${this.options.size}`;

        const closeButton = this.options.closable ? `
            <button class="modal-close" onclick="this.closest('.modal-overlay').classList.remove('active')">
                <i class="fas fa-times"></i>
            </button>
        ` : '';

        modal.innerHTML = `
            <div class="modal-header">
                <h3 class="modal-title">${escapeHtml(this.title)}</h3>
                ${closeButton}
            </div>
            <div class="modal-body">
                ${this.content}
            </div>
        `;

        return modal;
    }
}

// Confirmation Dialog
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    const content = `
        <p>${escapeHtml(message)}</p>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').classList.remove('active')">
                Cancel
            </button>
            <button class="btn btn-error" onclick="confirmAction()">
                Confirm
            </button>
        </div>
    `;

    const modal = new Modal(title, content, { size: 'small' });
    
    // Add confirm action to global scope temporarily
    window.confirmAction = () => {
        modal.hide();
        if (onConfirm) onConfirm();
        delete window.confirmAction;
    };

    modal.show();
}

// Data Table Component
class DataTable {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            columns: [],
            data: [],
            sortable: true,
            searchable: true,
            pagination: true,
            pageSize: 10,
            ...options
        };
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchTerm = '';
        this.filteredData = [];
        
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        const table = this.createTable();
        this.container.innerHTML = '';
        this.container.appendChild(table);
    }

    createTable() {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-container';

        // Search bar
        if (this.options.searchable) {
            const searchBar = document.createElement('div');
            searchBar.className = 'table-search';
            searchBar.innerHTML = `
                <input type="text" class="form-control" placeholder="Search..." value="${this.searchTerm}">
            `;
            wrapper.appendChild(searchBar);
        }

        // Table
        const table = document.createElement('table');
        table.className = 'table';

        // Header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        this.options.columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column.title;
            th.dataset.key = column.key;
            
            if (this.options.sortable && column.sortable !== false) {
                th.style.cursor = 'pointer';
                th.innerHTML += ' <i class="fas fa-sort"></i>';
            }
            
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);

        // Body
        const tbody = document.createElement('tbody');
        this.renderRows(tbody);
        table.appendChild(tbody);

        wrapper.appendChild(table);

        // Pagination
        if (this.options.pagination) {
            const pagination = this.createPagination();
            wrapper.appendChild(pagination);
        }

        return wrapper;
    }

    renderRows(tbody) {
        this.filteredData = this.getFilteredData();
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);

        tbody.innerHTML = '';

        pageData.forEach(row => {
            const tr = document.createElement('tr');
            
            this.options.columns.forEach(column => {
                const td = document.createElement('td');
                
                if (column.render) {
                    td.innerHTML = column.render(row[column.key], row);
                } else {
                    td.textContent = row[column.key] || '';
                }
                
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });

        if (pageData.length === 0) {
            const tr = document.createElement('tr');
            const td = document.createElement('td');
            td.colSpan = this.options.columns.length;
            td.textContent = 'No data available';
            td.style.textAlign = 'center';
            td.style.padding = '2rem';
            td.style.color = 'var(--text-secondary)';
            tr.appendChild(td);
            tbody.appendChild(tr);
        }
    }

    createPagination() {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        
        const pagination = document.createElement('div');
        pagination.className = 'table-pagination';
        
        const info = document.createElement('div');
        info.className = 'pagination-info';
        info.textContent = `Showing ${this.filteredData.length} entries`;
        
        const controls = document.createElement('div');
        controls.className = 'pagination-controls';
        
        // Previous button
        const prevBtn = document.createElement('button');
        prevBtn.className = 'btn btn-sm btn-secondary';
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.disabled = this.currentPage === 1;
        prevBtn.onclick = () => this.goToPage(this.currentPage - 1);
        controls.appendChild(prevBtn);
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `btn btn-sm ${i === this.currentPage ? 'btn-primary' : 'btn-secondary'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => this.goToPage(i);
                controls.appendChild(pageBtn);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                const ellipsis = document.createElement('span');
                ellipsis.textContent = '...';
                ellipsis.style.padding = '0 0.5rem';
                controls.appendChild(ellipsis);
            }
        }
        
        // Next button
        const nextBtn = document.createElement('button');
        nextBtn.className = 'btn btn-sm btn-secondary';
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = this.currentPage === totalPages;
        nextBtn.onclick = () => this.goToPage(this.currentPage + 1);
        controls.appendChild(nextBtn);
        
        pagination.appendChild(info);
        pagination.appendChild(controls);
        
        return pagination;
    }

    bindEvents() {
        // Search
        if (this.options.searchable) {
            const searchInput = this.container.querySelector('.table-search input');
            if (searchInput) {
                searchInput.addEventListener('input', debounce((e) => {
                    this.searchTerm = e.target.value;
                    this.currentPage = 1;
                    this.render();
                }, 300));
            }
        }

        // Sort
        if (this.options.sortable) {
            const headers = this.container.querySelectorAll('th[data-key]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const key = header.dataset.key;
                    this.sort(key);
                });
            });
        }
    }

    getFilteredData() {
        let data = [...this.options.data];

        // Apply search filter
        if (this.searchTerm) {
            data = data.filter(row => {
                return this.options.columns.some(column => {
                    const value = row[column.key];
                    return value && value.toString().toLowerCase().includes(this.searchTerm.toLowerCase());
                });
            });
        }

        // Apply sort
        if (this.sortColumn) {
            data.sort((a, b) => {
                const aVal = a[this.sortColumn];
                const bVal = b[this.sortColumn];
                
                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }

        return data;
    }

    sort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.currentPage = 1;
        this.render();
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.render();
        }
    }

    updateData(newData) {
        this.options.data = newData;
        this.currentPage = 1;
        this.render();
    }
}

// Notification Center Component
class NotificationCenter {
    constructor() {
        this.notifications = [];
        this.isOpen = false;
        this.unreadCount = 0;
        this.settings = {};

        this.init();
    }

    async init() {
        try {
            // Load initial data
            await this.loadNotifications();
            await this.loadSettings();

            // Set up event listeners
            this.setupEventListeners();

            // Update UI
            this.updateBadge();

            console.log('Notification center initialized');
        } catch (error) {
            console.error('Error initializing notification center:', error);
        }
    }

    setupEventListeners() {
        // Listen for new notifications
        if (window.electronAPI) {
            window.electronAPI.onNotificationUpdate((event, notification) => {
                this.addNotification(notification);
            });
        }
    }

    async loadNotifications() {
        try {
            if (window.electronAPI) {
                this.notifications = await window.electronAPI.getNotifications({ limit: 50 });
                this.updateUnreadCount();
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }

    async loadSettings() {
        try {
            if (window.electronAPI) {
                this.settings = await window.electronAPI.getNotificationSettings();
            }
        } catch (error) {
            console.error('Error loading notification settings:', error);
        }
    }

    addNotification(notification) {
        this.notifications.unshift(notification);
        this.updateUnreadCount();
        this.updateBadge();

        // Show toast if enabled
        if (this.settings.enabled && !this.isOpen) {
            this.showToast(notification);
        }

        // Update notification panel if open
        if (this.isOpen) {
            this.renderNotifications();
        }
    }

    showToast(notification) {
        const typeMap = {
            'server_start': 'success',
            'server_stop': 'warning',
            'server_error': 'error',
            'player_join': 'info',
            'player_leave': 'info',
            'login_failed': 'error',
            'backup_created': 'success',
            'backup_failed': 'error'
        };

        const type = typeMap[notification.type] || 'info';
        toastNotifications.show(notification.title, notification.message, type);
    }

    updateUnreadCount() {
        this.unreadCount = this.notifications.filter(n => !n.read).length;
    }

    updateBadge() {
        const badge = document.getElementById('notification-badge');
        if (badge) {
            if (this.unreadCount > 0) {
                badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        this.isOpen = true;
        const panel = document.getElementById('notification-panel');
        if (panel) {
            panel.classList.add('active');
            this.renderNotifications();
        }
    }

    close() {
        this.isOpen = false;
        const panel = document.getElementById('notification-panel');
        if (panel) {
            panel.classList.remove('active');
        }
    }

    renderNotifications() {
        const container = document.getElementById('notification-list');
        if (!container) return;

        if (this.notifications.length === 0) {
            container.innerHTML = `
                <div class="notification-empty">
                    <i class="fas fa-bell-slash"></i>
                    <p>No notifications</p>
                </div>
            `;
            return;
        }

        const notificationHTML = this.notifications.map(notification => {
            const timeAgo = this.getTimeAgo(notification.timestamp);
            const priorityClass = `priority-${notification.priority}`;
            const readClass = notification.read ? 'read' : 'unread';

            return `
                <div class="notification-item ${readClass} ${priorityClass}" data-id="${notification.id}">
                    <div class="notification-icon">
                        <i class="${this.getNotificationIcon(notification.type)}"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">${escapeHtml(notification.title)}</div>
                        <div class="notification-message">${escapeHtml(notification.message)}</div>
                        <div class="notification-time">${timeAgo}</div>
                    </div>
                    <div class="notification-actions">
                        ${!notification.read ? `
                            <button class="btn-icon" onclick="notificationCenter.markAsRead('${notification.id}')" title="Mark as read">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon" onclick="notificationCenter.deleteNotification('${notification.id}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = notificationHTML;
    }

    getNotificationIcon(type) {
        const iconMap = {
            'server_start': 'fas fa-play-circle text-success',
            'server_stop': 'fas fa-stop-circle text-warning',
            'server_restart': 'fas fa-redo text-info',
            'server_error': 'fas fa-exclamation-circle text-error',
            'player_join': 'fas fa-user-plus text-success',
            'player_leave': 'fas fa-user-minus text-warning',
            'player_banned': 'fas fa-ban text-error',
            'player_kicked': 'fas fa-user-times text-warning',
            'login_success': 'fas fa-sign-in-alt text-success',
            'login_failed': 'fas fa-exclamation-triangle text-error',
            'plugin_enabled': 'fas fa-puzzle-piece text-success',
            'plugin_disabled': 'fas fa-puzzle-piece text-warning',
            'backup_created': 'fas fa-save text-success',
            'backup_failed': 'fas fa-exclamation-circle text-error',
            'database_connected': 'fas fa-database text-success',
            'database_error': 'fas fa-database text-error'
        };

        return iconMap[type] || 'fas fa-bell text-info';
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;

        return time.toLocaleDateString();
    }

    async markAsRead(notificationId) {
        try {
            if (window.electronAPI) {
                await window.electronAPI.markNotificationRead(notificationId);

                // Update local state
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification) {
                    notification.read = true;
                    this.updateUnreadCount();
                    this.updateBadge();
                    this.renderNotifications();
                }
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    async markAllAsRead() {
        try {
            if (window.electronAPI) {
                await window.electronAPI.markAllNotificationsRead();

                // Update local state
                this.notifications.forEach(n => n.read = true);
                this.updateUnreadCount();
                this.updateBadge();
                this.renderNotifications();

                toastNotifications.show('Success', 'All notifications marked as read', 'success');
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    }

    async deleteNotification(notificationId) {
        try {
            if (window.electronAPI) {
                await window.electronAPI.deleteNotification(notificationId);

                // Update local state
                this.notifications = this.notifications.filter(n => n.id !== notificationId);
                this.updateUnreadCount();
                this.updateBadge();
                this.renderNotifications();
            }
        } catch (error) {
            console.error('Error deleting notification:', error);
        }
    }

    async clearAll() {
        try {
            if (window.electronAPI) {
                await window.electronAPI.clearAllNotifications();

                // Update local state
                this.notifications = [];
                this.updateUnreadCount();
                this.updateBadge();
                this.renderNotifications();
            }
        } catch (error) {
            console.error('Error clearing all notifications:', error);
        }
    }
}

// Global notification center instance
let notificationCenter;

const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const isDev = process.argv.includes('--dev');

// Keep a global reference of the window object
let mainWindow;
let splashWindow;

function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  splashWindow.loadFile('src/renderer/splash.html');
  
  splashWindow.on('closed', () => {
    splashWindow = null;
  });
}

function createMainWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    show: false,
    icon: path.join(__dirname, '../assets/icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Load the app
  mainWindow.loadFile('src/renderer/index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    if (splashWindow) {
      splashWindow.close();
    }
    mainWindow.show();
    
    // Open DevTools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Backup',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-action', 'new-backup');
          }
        },
        {
          label: 'Import Configuration',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'JSON Files', extensions: ['json'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            });
            
            if (!result.canceled) {
              mainWindow.webContents.send('menu-action', 'import-config', result.filePaths[0]);
            }
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Server',
      submenu: [
        {
          label: 'Start Server',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-action', 'start-server');
          }
        },
        {
          label: 'Stop Server',
          accelerator: 'CmdOrCtrl+T',
          click: () => {
            mainWindow.webContents.send('menu-action', 'stop-server');
          }
        },
        {
          label: 'Restart Server',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.webContents.send('menu-action', 'restart-server');
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Minecraft Server Dashboard',
              message: 'Minecraft Server Dashboard v1.0.0',
              detail: 'Professional Minecraft Server Administration Tool\nBuilt with Electron\n\nDeveloped by Hamza Damra'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createSplashWindow();
  
  // Small delay to show splash screen
  setTimeout(() => {
    createMainWindow();
    createMenu();
  }, 2000);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Import services
const ServerService = require('./services/ServerService');
const DatabaseService = require('./services/DatabaseService');
const LogService = require('./services/LogService');
const BackupService = require('./services/BackupService');
const PluginService = require('./services/PluginService');
const ConfigurationService = require('./services/ConfigurationService');
const SecurityService = require('./services/SecurityService');
const NotificationService = require('./services/NotificationService');

// Initialize services
let serverService;
let databaseService;
let logService;
let backupService;
let pluginService;
let configurationService;
let securityService;
let notificationService;

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

// Server Control IPC handlers
ipcMain.handle('start-server', async () => {
  try {
    const result = await serverService.startServer();
    mainWindow.webContents.send('notification', {
      title: 'Server',
      message: 'Server started successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to start server:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to start server: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('stop-server', async () => {
  try {
    const result = await serverService.stopServer();
    mainWindow.webContents.send('notification', {
      title: 'Server',
      message: 'Server stopped successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to stop server:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to stop server: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('restart-server', async () => {
  try {
    const result = await serverService.restartServer();
    mainWindow.webContents.send('notification', {
      title: 'Server',
      message: 'Server restarted successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to restart server:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to restart server: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('get-server-status', async () => {
  try {
    return await serverService.getServerStatus();
  } catch (error) {
    console.error('Failed to get server status:', error);
    return { status: 'unknown', error: error.message };
  }
});

ipcMain.handle('get-server-stats', async () => {
  try {
    return await serverService.getServerStats();
  } catch (error) {
    console.error('Failed to get server stats:', error);
    return null;
  }
});

// Player Management IPC handlers
ipcMain.handle('get-players', async () => {
  try {
    return await serverService.getOnlinePlayers();
  } catch (error) {
    console.error('Failed to get players:', error);
    return [];
  }
});

ipcMain.handle('kick-player', async (event, playerName, reason) => {
  try {
    const result = await serverService.kickPlayer(playerName, reason);
    mainWindow.webContents.send('notification', {
      title: 'Player Management',
      message: `Player ${playerName} has been kicked`,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to kick player:', error);
    throw error;
  }
});

ipcMain.handle('ban-player', async (event, playerName, reason) => {
  try {
    const result = await serverService.banPlayer(playerName, reason);
    mainWindow.webContents.send('notification', {
      title: 'Player Management',
      message: `Player ${playerName} has been banned`,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to ban player:', error);
    throw error;
  }
});

ipcMain.handle('unban-player', async (event, playerName) => {
  try {
    const result = await serverService.unbanPlayer(playerName);
    mainWindow.webContents.send('notification', {
      title: 'Player Management',
      message: `Player ${playerName} has been unbanned`,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to unban player:', error);
    throw error;
  }
});

// Database IPC handlers
ipcMain.handle('execute-query', async (event, query, params) => {
  try {
    return await databaseService.executeQuery(query, params);
  } catch (error) {
    console.error('Failed to execute query:', error);
    throw error;
  }
});

ipcMain.handle('get-player-data', async (event, playerName) => {
  try {
    return await databaseService.getPlayerData(playerName);
  } catch (error) {
    console.error('Failed to get player data:', error);
    return null;
  }
});

// Logs IPC handlers
ipcMain.handle('get-logs', async (event, lines = 100) => {
  try {
    return await logService.getLogs(lines);
  } catch (error) {
    console.error('Failed to get logs:', error);
    return [];
  }
});

// Backup IPC handlers
ipcMain.handle('create-backup', async (event, name, description) => {
  try {
    const result = await backupService.createBackup(name, description);
    mainWindow.webContents.send('notification', {
      title: 'Backup',
      message: 'Backup created successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to create backup:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to create backup: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('restore-backup', async (event, backupId) => {
  try {
    const result = await backupService.restoreBackup(backupId);
    mainWindow.webContents.send('notification', {
      title: 'Backup',
      message: 'Backup restored successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to restore backup:', error);
    throw error;
  }
});

ipcMain.handle('get-backups', async () => {
  try {
    return await backupService.getBackups();
  } catch (error) {
    console.error('Failed to get backups:', error);
    return [];
  }
});

ipcMain.handle('delete-backup', async (event, backupId) => {
  try {
    const result = await backupService.deleteBackup(backupId);
    mainWindow.webContents.send('notification', {
      title: 'Backup',
      message: 'Backup deleted successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to delete backup:', error);
    throw error;
  }
});

// Configuration IPC handlers
ipcMain.handle('get-server-config', async () => {
  try {
    return await serverService.getServerProperties();
  } catch (error) {
    console.error('Failed to get server config:', error);
    throw error;
  }
});

ipcMain.handle('update-server-config', async (event, config) => {
  try {
    const result = await serverService.updateServerProperties(config);
    mainWindow.webContents.send('notification', {
      title: 'Configuration',
      message: 'Server configuration updated successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to update server config:', error);
    throw error;
  }
});

ipcMain.handle('get-plugin-config', async (event, pluginName) => {
  try {
    return await serverService.getPluginConfig(pluginName);
  } catch (error) {
    console.error('Failed to get plugin config:', error);
    throw error;
  }
});

ipcMain.handle('update-plugin-config', async (event, pluginName, config) => {
  try {
    const result = await serverService.updatePluginConfig(pluginName, config);
    mainWindow.webContents.send('notification', {
      title: 'Plugin Configuration',
      message: `${pluginName} configuration updated successfully`,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to update plugin config:', error);
    throw error;
  }
});

// Plugin Management IPC handlers
ipcMain.handle('get-installed-plugins', async () => {
  try {
    return await pluginService.getInstalledPlugins();
  } catch (error) {
    console.error('Failed to get installed plugins:', error);
    return [];
  }
});

ipcMain.handle('enable-plugin', async (event, pluginId) => {
  try {
    const result = await pluginService.enablePlugin(pluginId);
    mainWindow.webContents.send('notification', {
      title: 'Plugin Management',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to enable plugin:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to enable plugin: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('disable-plugin', async (event, pluginId) => {
  try {
    const result = await pluginService.disablePlugin(pluginId);
    mainWindow.webContents.send('notification', {
      title: 'Plugin Management',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to disable plugin:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to disable plugin: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('delete-plugin', async (event, pluginId) => {
  try {
    const result = await pluginService.deletePlugin(pluginId);
    mainWindow.webContents.send('notification', {
      title: 'Plugin Management',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to delete plugin:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to delete plugin: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('get-plugin-config-content', async (event, pluginName) => {
  try {
    return await pluginService.getPluginConfig(pluginName);
  } catch (error) {
    console.error('Failed to get plugin config:', error);
    throw error;
  }
});

ipcMain.handle('update-plugin-config-content', async (event, pluginName, configContent) => {
  try {
    const result = await pluginService.updatePluginConfig(pluginName, configContent);
    mainWindow.webContents.send('notification', {
      title: 'Plugin Configuration',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to update plugin config:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to update plugin config: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('reload-plugins', async () => {
  try {
    const result = await pluginService.reloadPlugins();
    mainWindow.webContents.send('notification', {
      title: 'Plugin Management',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to reload plugins:', error);
    throw error;
  }
});

// Configuration Management IPC handlers
ipcMain.handle('get-server-properties', async () => {
  try {
    return await configurationService.getServerProperties();
  } catch (error) {
    console.error('Failed to get server properties:', error);
    throw error;
  }
});

ipcMain.handle('update-server-properties', async (event, properties) => {
  try {
    const result = await configurationService.updateServerProperties(properties);
    mainWindow.webContents.send('notification', {
      title: 'Configuration',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to update server properties:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to update server properties: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('get-yaml-config', async (event, configPath) => {
  try {
    return await configurationService.getYamlConfig(configPath);
  } catch (error) {
    console.error('Failed to get YAML config:', error);
    throw error;
  }
});

ipcMain.handle('update-yaml-config', async (event, configPath, configData) => {
  try {
    const result = await configurationService.updateYamlConfig(configPath, configData);
    mainWindow.webContents.send('notification', {
      title: 'Configuration',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to update YAML config:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to update configuration: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('validate-configuration', async (event, configPath, configData) => {
  try {
    return await configurationService.validateConfiguration(configPath, configData);
  } catch (error) {
    console.error('Failed to validate configuration:', error);
    throw error;
  }
});

ipcMain.handle('create-config-backup', async (event, configFileName) => {
  try {
    const result = await configurationService.createConfigBackup(configFileName);
    mainWindow.webContents.send('notification', {
      title: 'Configuration',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to create config backup:', error);
    throw error;
  }
});

ipcMain.handle('get-config-backups', async (event, configFileName) => {
  try {
    return await configurationService.getConfigBackups(configFileName);
  } catch (error) {
    console.error('Failed to get config backups:', error);
    return [];
  }
});

ipcMain.handle('restore-config-backup', async (event, backupPath, configFileName) => {
  try {
    const result = await configurationService.restoreConfigBackup(backupPath, configFileName);
    mainWindow.webContents.send('notification', {
      title: 'Configuration',
      message: result.message,
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to restore config backup:', error);
    throw error;
  }
});



// File Operations IPC handlers
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    return await serverService.readFile(filePath);
  } catch (error) {
    console.error('Failed to read file:', error);
    throw error;
  }
});

ipcMain.handle('write-file', async (event, filePath, content) => {
  try {
    const result = await serverService.writeFile(filePath, content);
    mainWindow.webContents.send('notification', {
      title: 'File Operation',
      message: 'File saved successfully',
      type: 'success'
    });
    return result;
  } catch (error) {
    console.error('Failed to write file:', error);
    throw error;
  }
});

// Authentication IPC handlers
ipcMain.handle('login', async (event, username, password) => {
  try {
    const result = await securityService.authenticate(username, password);

    if (result.success) {
      mainWindow.webContents.send('notification', {
        title: 'Authentication',
        message: `Welcome back, ${result.user.username}!`,
        type: 'success'
      });
    }

    return result;
  } catch (error) {
    console.error('Login failed:', error);
    mainWindow.webContents.send('notification', {
      title: 'Authentication Failed',
      message: error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('logout', async (event, sessionId) => {
  try {
    const result = await securityService.logout(sessionId);

    mainWindow.webContents.send('notification', {
      title: 'Authentication',
      message: 'Logged out successfully',
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Logout failed:', error);
    throw error;
  }
});

ipcMain.handle('validate-session', async (event, sessionId) => {
  try {
    return await securityService.validateSession(sessionId);
  } catch (error) {
    console.error('Session validation failed:', error);
    return { valid: false, reason: 'validation_error' };
  }
});

ipcMain.handle('get-current-user', async (event, sessionId) => {
  try {
    const sessionResult = await securityService.validateSession(sessionId);
    if (!sessionResult.valid) {
      return null;
    }

    const users = await securityService.getUsers();
    const user = users.find(u => u.id === sessionResult.session.userId);

    if (user) {
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      };
    }

    return null;
  } catch (error) {
    console.error('Failed to get current user:', error);
    return null;
  }
});

// User Management IPC handlers
ipcMain.handle('get-users', async () => {
  try {
    const users = await securityService.getUsers();
    // Remove password hashes from response
    return users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      created: user.created,
      lastLogin: user.lastLogin,
      active: user.active
    }));
  } catch (error) {
    console.error('Failed to get users:', error);
    return [];
  }
});

ipcMain.handle('create-user', async (event, userData) => {
  try {
    const result = await securityService.createUser(userData);

    mainWindow.webContents.send('notification', {
      title: 'User Management',
      message: `User ${userData.username} created successfully`,
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to create user:', error);
    mainWindow.webContents.send('notification', {
      title: 'Error',
      message: 'Failed to create user: ' + error.message,
      type: 'error'
    });
    throw error;
  }
});

ipcMain.handle('update-user', async (event, userData) => {
  try {
    const result = await securityService.updateUser(userData);

    mainWindow.webContents.send('notification', {
      title: 'User Management',
      message: `User ${userData.username} updated successfully`,
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to update user:', error);
    throw error;
  }
});

ipcMain.handle('delete-user', async (event, userId) => {
  try {
    const result = await securityService.deleteUser(userId);

    mainWindow.webContents.send('notification', {
      title: 'User Management',
      message: 'User deleted successfully',
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to delete user:', error);
    throw error;
  }
});

// Security Configuration IPC handlers
ipcMain.handle('get-security-config', async () => {
  try {
    return await securityService.getSecurityConfig();
  } catch (error) {
    console.error('Failed to get security config:', error);
    throw error;
  }
});

ipcMain.handle('update-security-config', async (event, config) => {
  try {
    const result = await securityService.updateSecurityConfig(config);

    mainWindow.webContents.send('notification', {
      title: 'Security',
      message: 'Security configuration updated successfully',
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to update security config:', error);
    throw error;
  }
});

ipcMain.handle('get-security-logs', async (event, limit) => {
  try {
    return await securityService.getSecurityLogs(limit);
  } catch (error) {
    console.error('Failed to get security logs:', error);
    return [];
  }
});

ipcMain.handle('get-active-sessions', async () => {
  try {
    return await securityService.getActiveSessions();
  } catch (error) {
    console.error('Failed to get active sessions:', error);
    return [];
  }
});

ipcMain.handle('invalidate-sessions', async (event, userId) => {
  try {
    const result = await securityService.invalidateAllSessions(userId);

    mainWindow.webContents.send('notification', {
      title: 'Security',
      message: 'Sessions invalidated successfully',
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to invalidate sessions:', error);
    throw error;
  }
});

// Notification System IPC handlers
ipcMain.handle('get-notifications', async (event, filters) => {
  try {
    return await notificationService.getNotifications(filters);
  } catch (error) {
    console.error('Failed to get notifications:', error);
    return [];
  }
});

ipcMain.handle('create-notification', async (event, type, title, message, data) => {
  try {
    return await notificationService.createNotification(type, title, message, data);
  } catch (error) {
    console.error('Failed to create notification:', error);
    throw error;
  }
});

ipcMain.handle('mark-notification-read', async (event, notificationId) => {
  try {
    return await notificationService.markAsRead(notificationId);
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
    return false;
  }
});

ipcMain.handle('mark-all-notifications-read', async (event, category) => {
  try {
    return await notificationService.markAllAsRead(category);
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error);
    return 0;
  }
});

ipcMain.handle('dismiss-notification', async (event, notificationId) => {
  try {
    return await notificationService.dismissNotification(notificationId);
  } catch (error) {
    console.error('Failed to dismiss notification:', error);
    return false;
  }
});

ipcMain.handle('delete-notification', async (event, notificationId) => {
  try {
    return await notificationService.deleteNotification(notificationId);
  } catch (error) {
    console.error('Failed to delete notification:', error);
    return false;
  }
});

ipcMain.handle('clear-all-notifications', async (event, category) => {
  try {
    const result = await notificationService.clearAllNotifications(category);

    mainWindow.webContents.send('notification', {
      title: 'Notifications',
      message: 'Notifications cleared successfully',
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to clear notifications:', error);
    throw error;
  }
});

ipcMain.handle('get-notification-settings', async () => {
  try {
    return await notificationService.getSettings();
  } catch (error) {
    console.error('Failed to get notification settings:', error);
    return {};
  }
});

ipcMain.handle('update-notification-settings', async (event, settings) => {
  try {
    const result = await notificationService.updateSettings(settings);

    mainWindow.webContents.send('notification', {
      title: 'Settings',
      message: 'Notification settings updated successfully',
      type: 'success'
    });

    return result;
  } catch (error) {
    console.error('Failed to update notification settings:', error);
    throw error;
  }
});

ipcMain.handle('get-notification-statistics', async () => {
  try {
    return await notificationService.getStatistics();
  } catch (error) {
    console.error('Failed to get notification statistics:', error);
    return { total: 0, unread: 0, read: 0, byCategory: {}, byPriority: {} };
  }
});

// App event handlers
app.whenReady().then(() => {
  // Initialize services
  serverService = new ServerService();
  databaseService = new DatabaseService();
  logService = new LogService();
  backupService = new BackupService();
  pluginService = new PluginService();
  configurationService = new ConfigurationService();
  securityService = new SecurityService();
  notificationService = new NotificationService();

  // Set up notification service to broadcast to main window
  notificationService.subscribe('main-window', (notification) => {
    if (mainWindow) {
      mainWindow.webContents.send('notification-update', notification);
    }
  });

  createSplashWindow();

  // Small delay to show splash screen
  setTimeout(() => {
    createMainWindow();
    createMenu();
  }, 2000);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createMainWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

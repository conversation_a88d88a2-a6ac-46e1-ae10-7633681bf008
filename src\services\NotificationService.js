const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');

class NotificationService extends EventEmitter {
    constructor() {
        super();
        this.notificationsPath = path.join(process.cwd(), 'data', 'notifications.json');
        this.settingsPath = path.join(process.cwd(), 'data', 'notification-settings.json');
        this.notifications = [];
        this.settings = {};
        this.subscribers = new Map();
        
        this.initializeService();
    }

    async initializeService() {
        try {
            await fs.mkdir(path.dirname(this.notificationsPath), { recursive: true });
            
            // Initialize notifications file
            try {
                await fs.access(this.notificationsPath);
                await this.loadNotifications();
            } catch (error) {
                const initialData = {
                    notifications: [],
                    lastUpdated: new Date().toISOString()
                };
                await fs.writeFile(this.notificationsPath, JSON.stringify(initialData, null, 2));
            }

            // Initialize settings file
            try {
                await fs.access(this.settingsPath);
                await this.loadSettings();
            } catch (error) {
                const defaultSettings = {
                    enabled: true,
                    soundEnabled: true,
                    desktopNotifications: true,
                    emailNotifications: false,
                    categories: {
                        server: { enabled: true, priority: 'high' },
                        security: { enabled: true, priority: 'critical' },
                        players: { enabled: true, priority: 'medium' },
                        plugins: { enabled: true, priority: 'low' },
                        backups: { enabled: true, priority: 'medium' },
                        database: { enabled: true, priority: 'high' }
                    },
                    retentionDays: 30,
                    maxNotifications: 1000
                };
                await fs.writeFile(this.settingsPath, JSON.stringify(defaultSettings, null, 2));
                this.settings = defaultSettings;
            }

            console.log('Notification service initialized');
        } catch (error) {
            console.error('Failed to initialize notification service:', error);
        }
    }

    async loadNotifications() {
        try {
            const content = await fs.readFile(this.notificationsPath, 'utf8');
            const data = JSON.parse(content);
            this.notifications = data.notifications || [];
        } catch (error) {
            console.error('Error loading notifications:', error);
            this.notifications = [];
        }
    }

    async saveNotifications() {
        try {
            const data = {
                notifications: this.notifications,
                lastUpdated: new Date().toISOString()
            };
            await fs.writeFile(this.notificationsPath, JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Error saving notifications:', error);
        }
    }

    async loadSettings() {
        try {
            const content = await fs.readFile(this.settingsPath, 'utf8');
            this.settings = JSON.parse(content);
        } catch (error) {
            console.error('Error loading notification settings:', error);
            this.settings = {};
        }
    }

    async saveSettings() {
        try {
            await fs.writeFile(this.settingsPath, JSON.stringify(this.settings, null, 2));
        } catch (error) {
            console.error('Error saving notification settings:', error);
        }
    }

    // Core notification methods
    async createNotification(type, title, message, data = {}) {
        try {
            if (!this.settings.enabled) {
                return null;
            }

            const category = this.getCategoryFromType(type);
            const categorySettings = this.settings.categories[category];
            
            if (!categorySettings || !categorySettings.enabled) {
                return null;
            }

            const notification = {
                id: this.generateId(),
                type: type,
                category: category,
                title: title,
                message: message,
                priority: categorySettings.priority,
                data: data,
                timestamp: new Date().toISOString(),
                read: false,
                dismissed: false
            };

            this.notifications.unshift(notification);
            
            // Cleanup old notifications
            await this.cleanupNotifications();
            
            // Save to file
            await this.saveNotifications();
            
            // Emit to subscribers
            this.emit('notification', notification);
            
            // Send to all connected clients
            this.broadcastNotification(notification);

            return notification;
        } catch (error) {
            console.error('Error creating notification:', error);
            return null;
        }
    }

    getCategoryFromType(type) {
        const typeMapping = {
            'server_start': 'server',
            'server_stop': 'server',
            'server_restart': 'server',
            'server_error': 'server',
            'player_join': 'players',
            'player_leave': 'players',
            'player_banned': 'players',
            'player_kicked': 'players',
            'login_success': 'security',
            'login_failed': 'security',
            'user_created': 'security',
            'plugin_enabled': 'plugins',
            'plugin_disabled': 'plugins',
            'plugin_error': 'plugins',
            'backup_created': 'backups',
            'backup_restored': 'backups',
            'backup_failed': 'backups',
            'database_connected': 'database',
            'database_error': 'database'
        };
        
        return typeMapping[type] || 'server';
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    async cleanupNotifications() {
        try {
            // Remove notifications older than retention period
            const retentionMs = this.settings.retentionDays * 24 * 60 * 60 * 1000;
            const cutoffDate = new Date(Date.now() - retentionMs);
            
            this.notifications = this.notifications.filter(notification => 
                new Date(notification.timestamp) > cutoffDate
            );

            // Limit total number of notifications
            if (this.notifications.length > this.settings.maxNotifications) {
                this.notifications = this.notifications.slice(0, this.settings.maxNotifications);
            }
        } catch (error) {
            console.error('Error cleaning up notifications:', error);
        }
    }

    // Notification management
    async getNotifications(filters = {}) {
        try {
            let filtered = [...this.notifications];

            if (filters.category) {
                filtered = filtered.filter(n => n.category === filters.category);
            }

            if (filters.priority) {
                filtered = filtered.filter(n => n.priority === filters.priority);
            }

            if (filters.read !== undefined) {
                filtered = filtered.filter(n => n.read === filters.read);
            }

            if (filters.dismissed !== undefined) {
                filtered = filtered.filter(n => n.dismissed === filters.dismissed);
            }

            if (filters.limit) {
                filtered = filtered.slice(0, filters.limit);
            }

            return filtered;
        } catch (error) {
            console.error('Error getting notifications:', error);
            return [];
        }
    }

    async markAsRead(notificationId) {
        try {
            const notification = this.notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.read = true;
                await this.saveNotifications();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error marking notification as read:', error);
            return false;
        }
    }

    async markAllAsRead(category = null) {
        try {
            let updated = 0;
            this.notifications.forEach(notification => {
                if (!notification.read && (!category || notification.category === category)) {
                    notification.read = true;
                    updated++;
                }
            });
            
            if (updated > 0) {
                await this.saveNotifications();
            }
            
            return updated;
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            return 0;
        }
    }

    async dismissNotification(notificationId) {
        try {
            const notification = this.notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.dismissed = true;
                await this.saveNotifications();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error dismissing notification:', error);
            return false;
        }
    }

    async deleteNotification(notificationId) {
        try {
            const index = this.notifications.findIndex(n => n.id === notificationId);
            if (index !== -1) {
                this.notifications.splice(index, 1);
                await this.saveNotifications();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error deleting notification:', error);
            return false;
        }
    }

    async clearAllNotifications(category = null) {
        try {
            if (category) {
                this.notifications = this.notifications.filter(n => n.category !== category);
            } else {
                this.notifications = [];
            }
            
            await this.saveNotifications();
            return true;
        } catch (error) {
            console.error('Error clearing notifications:', error);
            return false;
        }
    }

    // Settings management
    async getSettings() {
        return { ...this.settings };
    }

    async updateSettings(newSettings) {
        try {
            this.settings = { ...this.settings, ...newSettings };
            await this.saveSettings();
            return true;
        } catch (error) {
            console.error('Error updating notification settings:', error);
            return false;
        }
    }

    // Statistics
    async getStatistics() {
        try {
            const total = this.notifications.length;
            const unread = this.notifications.filter(n => !n.read).length;
            const byCategory = {};
            const byPriority = {};

            this.notifications.forEach(notification => {
                byCategory[notification.category] = (byCategory[notification.category] || 0) + 1;
                byPriority[notification.priority] = (byPriority[notification.priority] || 0) + 1;
            });

            return {
                total,
                unread,
                read: total - unread,
                byCategory,
                byPriority
            };
        } catch (error) {
            console.error('Error getting notification statistics:', error);
            return { total: 0, unread: 0, read: 0, byCategory: {}, byPriority: {} };
        }
    }

    // Broadcasting to clients
    broadcastNotification(notification) {
        this.subscribers.forEach((callback, subscriberId) => {
            try {
                callback(notification);
            } catch (error) {
                console.error(`Error broadcasting to subscriber ${subscriberId}:`, error);
            }
        });
    }

    subscribe(subscriberId, callback) {
        this.subscribers.set(subscriberId, callback);
    }

    unsubscribe(subscriberId) {
        this.subscribers.delete(subscriberId);
    }

    // Convenience methods for common notifications
    async notifyServerEvent(event, details = {}) {
        const messages = {
            'start': 'Server started successfully',
            'stop': 'Server stopped',
            'restart': 'Server restarted',
            'error': 'Server error occurred'
        };

        return await this.createNotification(
            `server_${event}`,
            'Server Status',
            messages[event] || `Server ${event}`,
            details
        );
    }

    async notifyPlayerEvent(event, playerName, details = {}) {
        const messages = {
            'join': `${playerName} joined the server`,
            'leave': `${playerName} left the server`,
            'banned': `${playerName} was banned`,
            'kicked': `${playerName} was kicked`
        };

        return await this.createNotification(
            `player_${event}`,
            'Player Activity',
            messages[event] || `Player ${event}: ${playerName}`,
            { player: playerName, ...details }
        );
    }

    async notifySecurityEvent(event, details = {}) {
        const messages = {
            'login_success': 'User logged in successfully',
            'login_failed': 'Failed login attempt',
            'user_created': 'New user account created'
        };

        return await this.createNotification(
            event,
            'Security Alert',
            messages[event] || `Security event: ${event}`,
            details
        );
    }
}

module.exports = NotificationService;

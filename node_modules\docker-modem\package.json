{"name": "docker-modem", "description": "Docker remote API network layer module.", "version": "5.0.6", "author": "<PERSON> <<EMAIL>>", "maintainers": ["apocas <<EMAIL>>"], "license": "Apache-2.0", "repository": {"type": "git", "url": "http://github.com/apocas/docker-modem.git"}, "keywords": ["containers", "api", "docker"], "dependencies": {"debug": "^4.1.1", "readable-stream": "^3.5.0", "split-ca": "^1.0.1", "ssh2": "^1.15.0"}, "devDependencies": {"chai": "~4.2.0", "mocha": "^10.2.0"}, "main": "./lib/modem", "scripts": {"test": "./node_modules/mocha/bin/mocha.js -R spec --exit"}, "engines": {"node": ">= 8.0"}}
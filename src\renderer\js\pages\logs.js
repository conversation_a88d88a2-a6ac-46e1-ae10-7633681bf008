// Server Logs Page
class LogsPage {
    static async render(container) {
        const html = `
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Total Log Entries</div>
                        <div class="stat-card-icon primary">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="logs-total-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-list"></i>
                        <span>Last 1000 entries</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Error Count</div>
                        <div class="stat-card-icon error">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="logs-error-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-bug"></i>
                        <span>Last 24 hours</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Warning Count</div>
                        <div class="stat-card-icon warning">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="logs-warning-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-flag"></i>
                        <span>Last 24 hours</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Log File Size</div>
                        <div class="stat-card-icon success">
                            <i class="fas fa-hdd"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="logs-file-size">0 MB</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-weight"></i>
                        <span>Current log</span>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Log Viewer</h3>
                    <p class="card-subtitle">Real-time server log monitoring with filtering</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-secondary" onclick="LogsPage.exportLogs()">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="LogsPage.clearLogs()">
                            <i class="fas fa-trash"></i>
                            Clear
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="LogsPage.refreshLogs()">
                            <i class="fas fa-sync"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="log-controls">
                        <div class="log-filters">
                            <div class="filter-group">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" id="log-search"
                                       placeholder="Search logs..."
                                       onkeyup="LogsPage.filterLogs()">
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Level</label>
                                <select class="form-control" id="log-level-filter" onchange="LogsPage.filterLogs()">
                                    <option value="">All Levels</option>
                                    <option value="error">Error</option>
                                    <option value="warning">Warning</option>
                                    <option value="info">Info</option>
                                    <option value="debug">Debug</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Lines</label>
                                <select class="form-control" id="log-lines-count" onchange="LogsPage.loadLogs()">
                                    <option value="100">Last 100</option>
                                    <option value="500" selected>Last 500</option>
                                    <option value="1000">Last 1000</option>
                                    <option value="5000">Last 5000</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="form-label">Auto-refresh</label>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="auto-refresh"
                                           onchange="LogsPage.toggleAutoRefresh()" checked>
                                    <label class="form-check-label" for="auto-refresh">
                                        Enable
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="log-viewer" id="log-viewer">
                        <div class="log-loading">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">Loading logs...</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dashboard-row">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Log Files</h3>
                        <p class="card-subtitle">Available log files</p>
                    </div>
                    <div class="card-body">
                        <div id="log-files-list"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Log Statistics</h3>
                        <p class="card-subtitle">Log analysis and patterns</p>
                    </div>
                    <div class="card-body">
                        <div class="log-stats" id="log-stats">
                            <div class="stat-item">
                                <div class="stat-label">Most Common Messages</div>
                                <div class="stat-value" id="common-messages">Loading...</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Activity Last Hour</div>
                                <div class="stat-value" id="activity-hour">0 entries</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Peak Activity Time</div>
                                <div class="stat-value" id="peak-time">N/A</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;

        // Initialize the page
        await this.initializePage();
    }

    static async initializePage() {
        this.logs = [];
        this.filteredLogs = [];
        this.autoRefreshInterval = null;

        // Load initial data
        await this.loadLogs();
        await this.loadLogFiles();
        await this.loadLogStats();

        // Start auto-refresh if enabled
        this.toggleAutoRefresh();
    }

    static async loadLogs() {
        try {
            const linesCount = document.getElementById('log-lines-count')?.value || 500;

            // Mock log data - in real implementation, this would call the log service
            const mockLogs = this.generateMockLogs(parseInt(linesCount));

            this.logs = mockLogs;
            this.filterLogs();

            // Update statistics
            this.updateLogStats();

        } catch (error) {
            console.error('Error loading logs:', error);
            const viewer = document.getElementById('log-viewer');
            if (viewer) {
                viewer.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load logs: ${error.message}
                    </div>
                `;
            }
        }
    }

    static generateMockLogs(count) {
        const logLevels = ['INFO', 'WARN', 'ERROR', 'DEBUG'];
        const logTypes = ['info', 'warning', 'error', 'debug'];
        const messages = [
            'Server started successfully',
            'Player Steve joined the game',
            'Player Alex left the game',
            'Saving world data...',
            'World saved successfully',
            'Plugin loaded: WorldEdit',
            'Plugin enabled: EssentialsX',
            'Memory usage: 85%',
            'TPS: 19.8',
            'Connection lost: timeout',
            'Failed to load chunk at coordinates',
            'Warning: High memory usage detected',
            'Error: Plugin failed to initialize',
            'Debug: Processing player movement',
            'Info: Backup completed successfully'
        ];

        const logs = [];
        const now = new Date();

        for (let i = count - 1; i >= 0; i--) {
            const timestamp = new Date(now.getTime() - i * 30000); // 30 seconds apart
            const levelIndex = Math.floor(Math.random() * logLevels.length);
            const level = logLevels[levelIndex];
            const type = logTypes[levelIndex];
            const message = messages[Math.floor(Math.random() * messages.length)];
            const time = timestamp.toTimeString().split(' ')[0];

            logs.push({
                id: `log_${timestamp.getTime()}_${i}`,
                timestamp: timestamp.toISOString(),
                time: time,
                thread: 'Server thread',
                level: level,
                message: message,
                type: type,
                raw: `[${time}] [Server thread/${level}]: ${message}`
            });
        }

        return logs;
    }

    static filterLogs() {
        const searchTerm = document.getElementById('log-search')?.value.toLowerCase() || '';
        const levelFilter = document.getElementById('log-level-filter')?.value || '';

        this.filteredLogs = this.logs.filter(log => {
            const matchesSearch = !searchTerm ||
                log.message.toLowerCase().includes(searchTerm) ||
                log.level.toLowerCase().includes(searchTerm);

            const matchesLevel = !levelFilter || log.type === levelFilter;

            return matchesSearch && matchesLevel;
        });

        this.renderLogs();
    }

    static renderLogs() {
        const viewer = document.getElementById('log-viewer');
        if (!viewer) return;

        if (this.filteredLogs.length === 0) {
            viewer.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    No logs match the current filters
                </div>
            `;
            return;
        }

        const logEntries = this.filteredLogs.map(log => `
            <div class="log-entry log-${log.type}" data-level="${log.type}">
                <div class="log-time">${log.time}</div>
                <div class="log-level">
                    <span class="log-level-badge log-level-${log.type}">${log.level}</span>
                </div>
                <div class="log-thread">${log.thread}</div>
                <div class="log-message">${escapeHtml(log.message)}</div>
            </div>
        `).join('');

        viewer.innerHTML = `
            <div class="log-container">
                ${logEntries}
            </div>
        `;

        // Auto-scroll to bottom
        viewer.scrollTop = viewer.scrollHeight;
    }

    static updateLogStats() {
        // Count by level
        const levelCounts = {
            error: 0,
            warning: 0,
            info: 0,
            debug: 0
        };

        this.logs.forEach(log => {
            if (levelCounts.hasOwnProperty(log.type)) {
                levelCounts[log.type]++;
            }
        });

        // Update stat cards
        document.getElementById('logs-total-count').textContent = this.logs.length;
        document.getElementById('logs-error-count').textContent = levelCounts.error;
        document.getElementById('logs-warning-count').textContent = levelCounts.warning;
        document.getElementById('logs-file-size').textContent = '2.5 MB'; // Mock value

        // Update detailed stats
        const commonMessages = this.getCommonMessages();
        const activityHour = this.logs.filter(log => {
            const logTime = new Date(log.timestamp);
            const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
            return logTime >= hourAgo;
        }).length;

        document.getElementById('common-messages').textContent = commonMessages[0] || 'No data';
        document.getElementById('activity-hour').textContent = `${activityHour} entries`;
        document.getElementById('peak-time').textContent = this.getPeakTime();
    }

    static getCommonMessages() {
        const messageCounts = {};

        this.logs.forEach(log => {
            const key = log.message.substring(0, 30) + '...';
            messageCounts[key] = (messageCounts[key] || 0) + 1;
        });

        return Object.entries(messageCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([message]) => message);
    }

    static getPeakTime() {
        const hourCounts = {};

        this.logs.forEach(log => {
            const hour = new Date(log.timestamp).getHours();
            hourCounts[hour] = (hourCounts[hour] || 0) + 1;
        });

        const peakHour = Object.entries(hourCounts)
            .sort(([,a], [,b]) => b - a)[0];

        return peakHour ? `${peakHour[0]}:00` : 'N/A';
    }

    static async loadLogFiles() {
        try {
            const container = document.getElementById('log-files-list');
            if (!container) return;

            // Mock log files data
            const logFiles = [
                {
                    name: 'latest.log',
                    size: '2.5 MB',
                    modified: new Date().toISOString(),
                    type: 'current'
                },
                {
                    name: '2024-01-25-1.log.gz',
                    size: '15.2 MB',
                    modified: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                    type: 'archived'
                },
                {
                    name: '2024-01-24-1.log.gz',
                    size: '18.7 MB',
                    modified: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
                    type: 'archived'
                }
            ];

            const filesList = logFiles.map(file => `
                <div class="log-file-item">
                    <div class="file-info">
                        <div class="file-name">
                            <i class="fas fa-file-alt"></i>
                            ${file.name}
                        </div>
                        <div class="file-details">
                            <span class="badge badge-secondary">${file.size}</span>
                            <span class="badge badge-${file.type === 'current' ? 'success' : 'secondary'}">${file.type}</span>
                        </div>
                        <div class="file-date">${formatRelativeTime(file.modified)}</div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-primary" onclick="LogsPage.viewLogFile('${file.name}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="LogsPage.downloadLogFile('${file.name}')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = filesList;

        } catch (error) {
            console.error('Error loading log files:', error);
        }
    }

    static async loadLogStats() {
        // Stats are updated in updateLogStats()
    }

    static toggleAutoRefresh() {
        const checkbox = document.getElementById('auto-refresh');
        const isEnabled = checkbox?.checked || false;

        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }

        if (isEnabled) {
            this.autoRefreshInterval = setInterval(() => {
                this.loadLogs();
            }, 5000); // Refresh every 5 seconds
        }
    }

    static async refreshLogs() {
        await this.loadLogs();
        toastNotifications.show('Success', 'Logs refreshed', 'success');
    }

    static clearLogs() {
        showConfirmDialog(
            'Clear Logs',
            'Are you sure you want to clear the log viewer? This will not delete the actual log files.',
            () => {
                this.logs = [];
                this.filteredLogs = [];
                this.renderLogs();
                this.updateLogStats();
                toastNotifications.show('Success', 'Log viewer cleared', 'success');
            }
        );
    }

    static exportLogs() {
        try {
            const data = this.filteredLogs.map(log => log.raw).join('\n');
            const filename = `server_logs_${new Date().toISOString().split('T')[0]}.txt`;

            downloadFile(data, filename, 'text/plain');
            toastNotifications.show('Success', 'Logs exported successfully', 'success');
        } catch (error) {
            console.error('Error exporting logs:', error);
            toastNotifications.show('Error', 'Failed to export logs', 'error');
        }
    }

    static viewLogFile(filename) {
        toastNotifications.show('Info', `Viewing ${filename}...`, 'info');
        // Implementation would load and display the specific log file
    }

    static downloadLogFile(filename) {
        toastNotifications.show('Info', `Downloading ${filename}...`, 'info');
        // Implementation would download the log file
    }

    static cleanup() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }
    }
}

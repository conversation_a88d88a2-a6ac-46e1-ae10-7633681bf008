// Server Settings Page
class SettingsPage {
    static async render(container) {
        const html = `
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Configuration Files</div>
                        <div class="stat-card-icon primary">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="config-files-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-cog"></i>
                        <span>Files</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Backups Available</div>
                        <div class="stat-card-icon success">
                            <i class="fas fa-save"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="config-backups-count">0</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-history"></i>
                        <span>Backups</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Last Modified</div>
                        <div class="stat-card-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="last-modified-time">Never</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-edit"></i>
                        <span>Modified</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-card-header">
                        <div class="stat-card-title">Validation Status</div>
                        <div class="stat-card-icon" id="validation-status-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-card-value" id="validation-status">Valid</div>
                    <div class="stat-card-change neutral">
                        <i class="fas fa-shield-alt"></i>
                        <span>Status</span>
                    </div>
                </div>
            </div>

            <div class="dashboard-row">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Server Properties</h3>
                        <p class="card-subtitle">Configure basic server settings</p>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-secondary" onclick="SettingsPage.createBackup('server.properties')">
                                <i class="fas fa-save"></i>
                                Backup
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="SettingsPage.saveServerProperties()">
                                <i class="fas fa-check"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="settings-form" id="server-properties-form">
                            <!-- Server properties form will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Configuration Files</h3>
                        <p class="card-subtitle">Manage YAML configuration files</p>
                    </div>
                    <div class="card-body">
                        <div class="config-files-list" id="config-files-list">
                            <!-- Configuration files list will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Configuration Backups</h3>
                    <p class="card-subtitle">Manage configuration file backups</p>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-primary" onclick="SettingsPage.refreshBackups()">
                            <i class="fas fa-sync"></i>
                            Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="config-backups-table"></div>
                </div>
            </div>

            <!-- Configuration Editor Modal -->
            <div class="modal-overlay" id="config-editor-modal">
                <div class="modal modal-large">
                    <div class="modal-header">
                        <h3 class="modal-title" id="config-editor-title">Configuration Editor</h3>
                        <button class="modal-close" onclick="SettingsPage.closeConfigEditor()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="config-editor-container">
                            <div class="config-editor-toolbar">
                                <button class="btn btn-sm btn-secondary" onclick="SettingsPage.validateConfig()">
                                    <i class="fas fa-check-circle"></i>
                                    Validate
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="SettingsPage.resetConfig()">
                                    <i class="fas fa-undo"></i>
                                    Reset
                                </button>
                                <div class="validation-status" id="config-validation-status"></div>
                            </div>
                            <textarea class="form-control config-editor" id="config-editor-content"
                                      rows="25" placeholder="Configuration content will be loaded here..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="SettingsPage.closeConfigEditor()">
                            Cancel
                        </button>
                        <button class="btn btn-primary" onclick="SettingsPage.saveConfigFile()">
                            <i class="fas fa-save"></i>
                            Save Configuration
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;

        // Initialize the page
        await this.initializePage();
    }

    static async initializePage() {
        this.currentConfigFile = null;
        this.serverProperties = {};
        this.configFiles = [
            'bukkit.yml',
            'spigot.yml',
            'paper.yml',
            'paper-global.yml',
            'paper-world-defaults.yml'
        ];

        // Load initial data
        await this.loadServerProperties();
        await this.loadConfigFiles();
        await this.loadConfigBackups();
        await this.updateStats();
    }

    static async loadServerProperties() {
        try {
            if (window.electronAPI) {
                const result = await window.electronAPI.getServerProperties();
                this.serverProperties = result.properties;
                this.renderServerPropertiesForm();
            }
        } catch (error) {
            console.error('Error loading server properties:', error);
            toastNotifications.show('Error', 'Failed to load server properties: ' + error.message, 'error');
        }
    }

    static renderServerPropertiesForm() {
        const container = document.getElementById('server-properties-form');
        if (!container) return;

        const commonProperties = [
            { key: 'server-port', label: 'Server Port', type: 'number', description: 'Port for the server to listen on' },
            { key: 'max-players', label: 'Max Players', type: 'number', description: 'Maximum number of players' },
            { key: 'difficulty', label: 'Difficulty', type: 'select', options: ['peaceful', 'easy', 'normal', 'hard'], description: 'Game difficulty' },
            { key: 'gamemode', label: 'Default Gamemode', type: 'select', options: ['survival', 'creative', 'adventure', 'spectator'], description: 'Default game mode for new players' },
            { key: 'motd', label: 'MOTD', type: 'text', description: 'Message of the day' },
            { key: 'online-mode', label: 'Online Mode', type: 'boolean', description: 'Enable Mojang account verification' },
            { key: 'pvp', label: 'PvP', type: 'boolean', description: 'Enable player vs player combat' },
            { key: 'spawn-protection', label: 'Spawn Protection', type: 'number', description: 'Radius of spawn protection' },
            { key: 'view-distance', label: 'View Distance', type: 'number', description: 'Server view distance in chunks' },
            { key: 'simulation-distance', label: 'Simulation Distance', type: 'number', description: 'Distance for mob spawning and block updates' }
        ];

        const formHTML = commonProperties.map(prop => {
            const value = this.serverProperties[prop.key] || '';

            let inputHTML = '';
            if (prop.type === 'select') {
                const options = prop.options.map(opt =>
                    `<option value="${opt}" ${value === opt ? 'selected' : ''}>${opt}</option>`
                ).join('');
                inputHTML = `<select class="form-control" id="prop-${prop.key}">${options}</select>`;
            } else if (prop.type === 'boolean') {
                const checked = value === 'true' ? 'checked' : '';
                inputHTML = `<input type="checkbox" class="form-check-input" id="prop-${prop.key}" ${checked}>`;
            } else {
                inputHTML = `<input type="${prop.type}" class="form-control" id="prop-${prop.key}" value="${value}">`;
            }

            return `
                <div class="form-group">
                    <label class="form-label" for="prop-${prop.key}">${prop.label}</label>
                    ${inputHTML}
                    <div class="form-text">${prop.description}</div>
                </div>
            `;
        }).join('');

        container.innerHTML = formHTML;
    }

    static async saveServerProperties() {
        try {
            const properties = {};

            // Collect form values
            const formElements = document.querySelectorAll('#server-properties-form input, #server-properties-form select');
            formElements.forEach(element => {
                const key = element.id.replace('prop-', '');
                if (element.type === 'checkbox') {
                    properties[key] = element.checked ? 'true' : 'false';
                } else {
                    properties[key] = element.value;
                }
            });

            if (window.electronAPI) {
                await window.electronAPI.updateServerProperties(properties);
                this.serverProperties = properties;
                await this.updateStats();
            }
        } catch (error) {
            console.error('Error saving server properties:', error);
            toastNotifications.show('Error', 'Failed to save server properties: ' + error.message, 'error');
        }
    }

    static async loadConfigFiles() {
        try {
            const container = document.getElementById('config-files-list');
            if (!container) return;

            const fileListHTML = this.configFiles.map(file => `
                <div class="config-file-item">
                    <div class="config-file-info">
                        <div class="config-file-name">${file}</div>
                        <div class="config-file-description">Server configuration file</div>
                    </div>
                    <div class="config-file-actions">
                        <button class="btn btn-sm btn-primary" onclick="SettingsPage.editConfigFile('${file}')">
                            <i class="fas fa-edit"></i>
                            Edit
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="SettingsPage.createBackup('${file}')">
                            <i class="fas fa-save"></i>
                            Backup
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = fileListHTML;
        } catch (error) {
            console.error('Error loading config files:', error);
        }
    }

    static async editConfigFile(configPath) {
        try {
            this.currentConfigFile = configPath;

            const modal = document.getElementById('config-editor-modal');
            const title = document.getElementById('config-editor-title');
            const content = document.getElementById('config-editor-content');

            title.textContent = `Edit ${configPath}`;
            content.value = 'Loading configuration...';

            modal.classList.add('active');

            if (window.electronAPI) {
                try {
                    const result = await window.electronAPI.getYamlConfig(configPath);
                    content.value = result.raw;
                } catch (error) {
                    content.value = `# Error loading configuration: ${error.message}
# Please check if the file exists and is readable`;
                    console.error('Error loading config file:', error);
                }
            }
        } catch (error) {
            console.error('Error opening config editor:', error);
            toastNotifications.show('Error', 'Failed to open configuration editor: ' + error.message, 'error');
        }
    }

    static closeConfigEditor() {
        const modal = document.getElementById('config-editor-modal');
        modal.classList.remove('active');
        this.currentConfigFile = null;
    }

    static async validateConfig() {
        if (!this.currentConfigFile) return;

        try {
            const content = document.getElementById('config-editor-content').value;
            const statusDiv = document.getElementById('config-validation-status');

            if (window.electronAPI) {
                const result = await window.electronAPI.validateConfiguration(this.currentConfigFile, content);

                if (result.valid) {
                    statusDiv.innerHTML = '<span class="text-success"><i class="fas fa-check-circle"></i> Valid configuration</span>';
                } else {
                    const errors = result.errors.map(error => `<li>${error}</li>`).join('');
                    statusDiv.innerHTML = `<span class="text-error"><i class="fas fa-exclamation-circle"></i> Validation errors:</span><ul>${errors}</ul>`;
                }
            }
        } catch (error) {
            console.error('Error validating config:', error);
            const statusDiv = document.getElementById('config-validation-status');
            statusDiv.innerHTML = `<span class="text-error"><i class="fas fa-exclamation-circle"></i> Validation failed: ${error.message}</span>`;
        }
    }

    static async saveConfigFile() {
        if (!this.currentConfigFile) return;

        try {
            const content = document.getElementById('config-editor-content').value;

            if (window.electronAPI) {
                await window.electronAPI.updateYamlConfig(this.currentConfigFile, content);
                this.closeConfigEditor();
                await this.updateStats();
            }
        } catch (error) {
            console.error('Error saving config file:', error);
            toastNotifications.show('Error', 'Failed to save configuration: ' + error.message, 'error');
        }
    }

    static async createBackup(configFileName) {
        try {
            if (window.electronAPI) {
                await window.electronAPI.createConfigBackup(configFileName);
                await this.loadConfigBackups();
                await this.updateStats();
            }
        } catch (error) {
            console.error('Error creating backup:', error);
            toastNotifications.show('Error', 'Failed to create backup: ' + error.message, 'error');
        }
    }

    static async loadConfigBackups() {
        try {
            const container = document.getElementById('config-backups-table');
            if (!container) return;

            const allBackups = [];

            if (window.electronAPI) {
                // Load backups for all config files
                for (const configFile of ['server.properties', ...this.configFiles]) {
                    try {
                        const backups = await window.electronAPI.getConfigBackups(configFile);
                        backups.forEach(backup => {
                            backup.configFile = configFile;
                            allBackups.push(backup);
                        });
                    } catch (error) {
                        console.error(`Error loading backups for ${configFile}:`, error);
                    }
                }
            }

            // Sort by creation date (newest first)
            allBackups.sort((a, b) => new Date(b.created) - new Date(a.created));

            if (allBackups.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        No configuration backups found
                    </div>
                `;
                return;
            }

            const table = new DataTable(container, {
                columns: [
                    { key: 'configFile', title: 'Configuration File', sortable: true },
                    {
                        key: 'created',
                        title: 'Created',
                        sortable: true,
                        render: (value) => new Date(value).toLocaleString()
                    },
                    {
                        key: 'size',
                        title: 'Size',
                        sortable: true,
                        render: (value) => formatBytes(value)
                    },
                    {
                        key: 'actions',
                        title: 'Actions',
                        render: (value, row) => `
                            <div class="btn-group">
                                <button class="btn btn-sm btn-primary" onclick="SettingsPage.restoreBackup('${row.path}', '${row.configFile}')" title="Restore">
                                    <i class="fas fa-undo"></i>
                                </button>
                                <button class="btn btn-sm btn-error" onclick="SettingsPage.deleteBackup('${row.path}')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `
                    }
                ],
                data: allBackups,
                pagination: true,
                pageSize: 10
            });

            this.configBackups = allBackups;
        } catch (error) {
            console.error('Error loading config backups:', error);
        }
    }

    static async restoreBackup(backupPath, configFileName) {
        showConfirmDialog(
            'Restore Configuration Backup',
            `Are you sure you want to restore ${configFileName} from this backup? Current configuration will be backed up first.`,
            async () => {
                try {
                    if (window.electronAPI) {
                        await window.electronAPI.restoreConfigBackup(backupPath, configFileName);
                        await this.loadServerProperties();
                        await this.loadConfigBackups();
                        await this.updateStats();
                    }
                } catch (error) {
                    console.error('Error restoring backup:', error);
                    toastNotifications.show('Error', 'Failed to restore backup: ' + error.message, 'error');
                }
            }
        );
    }

    static async deleteBackup(backupPath) {
        showConfirmDialog(
            'Delete Configuration Backup',
            'Are you sure you want to delete this backup? This action cannot be undone.',
            async () => {
                try {
                    // Note: We would need to add a deleteConfigBackup method to the service
                    // For now, just refresh the list
                    await this.loadConfigBackups();
                    await this.updateStats();
                    toastNotifications.show('Success', 'Backup deleted successfully', 'success');
                } catch (error) {
                    console.error('Error deleting backup:', error);
                    toastNotifications.show('Error', 'Failed to delete backup: ' + error.message, 'error');
                }
            }
        );
    }

    static async refreshBackups() {
        await this.loadConfigBackups();
        await this.updateStats();
        toastNotifications.show('Success', 'Configuration backups refreshed', 'success');
    }

    static async updateStats() {
        try {
            // Update configuration files count
            document.getElementById('config-files-count').textContent = this.configFiles.length + 1; // +1 for server.properties

            // Update backups count
            const backupsCount = this.configBackups ? this.configBackups.length : 0;
            document.getElementById('config-backups-count').textContent = backupsCount;

            // Update last modified time (mock for now)
            document.getElementById('last-modified-time').textContent = 'Just now';

            // Update validation status
            document.getElementById('validation-status').textContent = 'Valid';
            document.getElementById('validation-status-icon').className = 'stat-card-icon success';
            document.getElementById('validation-status-icon').innerHTML = '<i class="fas fa-check-circle"></i>';

        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    static resetConfig() {
        showConfirmDialog(
            'Reset Configuration',
            'Are you sure you want to reset the configuration to its original state? Unsaved changes will be lost.',
            async () => {
                if (this.currentConfigFile) {
                    await this.editConfigFile(this.currentConfigFile);
                }
            }
        );
    }
}

{"name": "minecraft-server-dashboard", "version": "1.0.0", "description": "Professional Minecraft Server Administration Dashboard", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "keywords": ["minecraft", "server", "dashboard", "admin", "electron"], "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "mysql2": "^3.6.5", "dockerode": "^4.0.2", "node-cron": "^3.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "winston": "^3.11.0", "chokidar": "^3.5.3", "archiver": "^6.0.1", "extract-zip": "^2.0.1", "moment": "^2.29.4", "chart.js": "^4.4.1", "axios": "^1.6.2", "js-yaml": "^4.1.0"}, "build": {"appId": "com.hamzadamra.minecraft-dashboard", "productName": "Minecraft Server Dashboard", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}